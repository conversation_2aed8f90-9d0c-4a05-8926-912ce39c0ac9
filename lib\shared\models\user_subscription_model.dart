class UserSubscription {
  final String id;
  final String deviceId; // معرف الجهاز الفريد
  final List<String> subscribedSubjectIds; // المواد المشترك بها
  final Map<String, DateTime> subjectActivationDates; // تاريخ تفعيل كل مادة
  final List<String> usedCodes; // الأكواد المستخدمة
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  UserSubscription({
    required this.id,
    required this.deviceId,
    this.subscribedSubjectIds = const [],
    this.subjectActivationDates = const {},
    this.usedCodes = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  factory UserSubscription.fromMap(Map<String, dynamic> map) {
    // تحويل subjectActivationDates من Map<String, int> إلى Map<String, DateTime>
    Map<String, DateTime> activationDates = {};
    if (map['subjectActivationDates'] != null) {
      Map<String, dynamic> datesMap = Map<String, dynamic>.from(
        map['subjectActivationDates'],
      );
      datesMap.forEach((key, value) {
        activationDates[key] = DateTime.fromMillisecondsSinceEpoch(value);
      });
    }

    return UserSubscription(
      id: map['id'] ?? '',
      deviceId: map['deviceId'] ?? '',
      subscribedSubjectIds: List<String>.from(
        map['subscribedSubjectIds'] ?? [],
      ),
      subjectActivationDates: activationDates,
      usedCodes: List<String>.from(map['usedCodes'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    // تحويل subjectActivationDates من Map<String, DateTime> إلى Map<String, int>
    Map<String, int> activationDatesMap = {};
    subjectActivationDates.forEach((key, value) {
      activationDatesMap[key] = value.millisecondsSinceEpoch;
    });

    return {
      'id': id,
      'deviceId': deviceId,
      'subscribedSubjectIds': subscribedSubjectIds,
      'subjectActivationDates': activationDatesMap,
      'usedCodes': usedCodes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  bool isSubscribedToSubject(String subjectId) {
    // إذا كان المستخدم مشترك في "جميع المواد"، فهو مشترك في جميع المواد
    if (subscribedSubjectIds.contains('all_subjects')) {
      return true;
    }
    return subscribedSubjectIds.contains(subjectId);
  }

  DateTime? getSubjectActivationDate(String subjectId) {
    return subjectActivationDates[subjectId];
  }

  bool hasUsedCode(String code) {
    return usedCodes.contains(code);
  }

  UserSubscription copyWith({
    String? id,
    String? deviceId,
    List<String>? subscribedSubjectIds,
    Map<String, DateTime>? subjectActivationDates,
    List<String>? usedCodes,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return UserSubscription(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      subscribedSubjectIds: subscribedSubjectIds ?? this.subscribedSubjectIds,
      subjectActivationDates:
          subjectActivationDates ?? this.subjectActivationDates,
      usedCodes: usedCodes ?? this.usedCodes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  // إضافة اشتراك جديد
  UserSubscription addSubscription(
    List<String> newSubjectIds,
    String codeUsed,
  ) {
    final now = DateTime.now();
    final updatedSubjects = List<String>.from(subscribedSubjectIds);
    final updatedActivationDates = Map<String, DateTime>.from(
      subjectActivationDates,
    );
    final updatedUsedCodes = List<String>.from(usedCodes);

    // إضافة المواد الجديدة
    for (String subjectId in newSubjectIds) {
      if (!updatedSubjects.contains(subjectId)) {
        updatedSubjects.add(subjectId);
        updatedActivationDates[subjectId] = now;
      }
    }

    // إضافة الكود المستخدم
    if (!updatedUsedCodes.contains(codeUsed)) {
      updatedUsedCodes.add(codeUsed);
    }

    return copyWith(
      subscribedSubjectIds: updatedSubjects,
      subjectActivationDates: updatedActivationDates,
      usedCodes: updatedUsedCodes,
      updatedAt: now,
    );
  }
}
