import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import 'encryption_service.dart';
import 'device_service.dart';

/// خدمة إدارة الفيديوهات مع التشفير والحماية
class VideoService extends ChangeNotifier {
  static final VideoService _instance = VideoService._internal();
  static VideoService get instance => _instance;
  VideoService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final EncryptionService _encryption = EncryptionService.instance;
  final Dio _dio = Dio();

  // مجلد حفظ الفيديوهات المشفرة
  Directory? _videosDirectory;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _encryption.initialize();
    await _initializeVideosDirectory();
  }

  /// تهيئة مجلد الفيديوهات
  Future<void> _initializeVideosDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _videosDirectory = Directory('${appDir.path}/encrypted_videos');

      if (!await _videosDirectory!.exists()) {
        await _videosDirectory!.create(recursive: true);
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة مجلد الفيديوهات: $e');
    }
  }

  // ===== إدارة أقسام الفيديوهات =====

  /// الحصول على جميع أقسام الفيديوهات
  Future<List<VideoSection>> getVideoSections() async {
    try {
      final snapshot = await _firestore
          .collection('video_sections')
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => VideoSection.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل أقسام الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة قسم فيديوهات جديد
  Future<String> addVideoSection(VideoSection section) async {
    try {
      final docRef = await _firestore
          .collection('video_sections')
          .add(section.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة قسم الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث قسم فيديوهات
  Future<void> updateVideoSection(VideoSection section) async {
    try {
      await _firestore
          .collection('video_sections')
          .doc(section.id)
          .update(section.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قسم الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف قسم فيديوهات
  Future<void> deleteVideoSection(String sectionId) async {
    try {
      final batch = _firestore.batch();

      // حذف جميع المواد والوحدات والدروس والفيديوهات المرتبطة
      final subjectsSnapshot = await _firestore
          .collection('video_subjects')
          .where('sectionId', isEqualTo: sectionId)
          .get();

      for (final subjectDoc in subjectsSnapshot.docs) {
        await _deleteVideoSubjectData(subjectDoc.id, batch);
      }

      // حذف القسم نفسه
      batch.delete(_firestore.collection('video_sections').doc(sectionId));

      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف قسم الفيديوهات: $e');
      rethrow;
    }
  }

  // ===== إدارة مواد الفيديوهات =====

  /// الحصول على مواد الفيديوهات لقسم معين
  Future<List<VideoSubject>> getVideoSubjects(String sectionId) async {
    try {
      final snapshot = await _firestore
          .collection('video_subjects')
          .where('sectionId', isEqualTo: sectionId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => VideoSubject.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل مواد الفيديوهات: $e');
      return [];
    }
  }

  /// الحصول على جميع مواد الفيديوهات (للأدمن)
  Future<List<VideoSubject>> getAllVideoSubjects() async {
    try {
      final snapshot = await _firestore
          .collection('video_subjects')
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => VideoSubject.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل جميع مواد الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة مادة فيديوهات جديدة
  Future<String> addVideoSubject(VideoSubject subject) async {
    try {
      final docRef = await _firestore
          .collection('video_subjects')
          .add(subject.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث مادة فيديوهات
  Future<void> updateVideoSubject(VideoSubject subject) async {
    try {
      await _firestore
          .collection('video_subjects')
          .doc(subject.id)
          .update(subject.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف مادة فيديوهات
  Future<void> deleteVideoSubject(String subjectId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoSubjectData(subjectId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف مادة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات مادة الفيديوهات (مساعدة)
  Future<void> _deleteVideoSubjectData(
    String subjectId,
    WriteBatch batch,
  ) async {
    // حذف الوحدات والدروس والفيديوهات
    final unitsSnapshot = await _firestore
        .collection('video_units')
        .where('subjectId', isEqualTo: subjectId)
        .get();

    for (final unitDoc in unitsSnapshot.docs) {
      await _deleteVideoUnitData(unitDoc.id, batch);
    }

    // حذف المادة نفسها
    batch.delete(_firestore.collection('video_subjects').doc(subjectId));
  }

  // ===== إدارة وحدات الفيديوهات =====

  /// الحصول على وحدات الفيديوهات لمادة معينة
  Future<List<VideoUnit>> getVideoUnits(String subjectId) async {
    try {
      final snapshot = await _firestore
          .collection('video_units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => VideoUnit.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل وحدات الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة وحدة فيديوهات جديدة
  Future<String> addVideoUnit(VideoUnit unit) async {
    try {
      final docRef = await _firestore
          .collection('video_units')
          .add(unit.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث وحدة فيديوهات
  Future<void> updateVideoUnit(VideoUnit unit) async {
    try {
      await _firestore
          .collection('video_units')
          .doc(unit.id)
          .update(unit.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف وحدة فيديوهات
  Future<void> deleteVideoUnit(String unitId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoUnitData(unitId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف وحدة الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات وحدة الفيديوهات (مساعدة)
  Future<void> _deleteVideoUnitData(String unitId, WriteBatch batch) async {
    // حذف الدروس والفيديوهات
    final lessonsSnapshot = await _firestore
        .collection('video_lessons')
        .where('unitId', isEqualTo: unitId)
        .get();

    for (final lessonDoc in lessonsSnapshot.docs) {
      await _deleteVideoLessonData(lessonDoc.id, batch);
    }

    // حذف الوحدة نفسها
    batch.delete(_firestore.collection('video_units').doc(unitId));
  }

  // ===== إدارة دروس الفيديوهات =====

  /// الحصول على دروس الفيديوهات لوحدة معينة
  Future<List<VideoLesson>> getVideoLessons(String unitId) async {
    try {
      final snapshot = await _firestore
          .collection('video_lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => VideoLesson.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل دروس الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة درس فيديوهات جديد
  Future<String> addVideoLesson(VideoLesson lesson) async {
    try {
      final docRef = await _firestore
          .collection('video_lessons')
          .add(lesson.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// تحديث درس فيديوهات
  Future<void> updateVideoLesson(VideoLesson lesson) async {
    try {
      await _firestore
          .collection('video_lessons')
          .doc(lesson.id)
          .update(lesson.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف درس فيديوهات
  Future<void> deleteVideoLesson(String lessonId) async {
    try {
      final batch = _firestore.batch();
      await _deleteVideoLessonData(lessonId, batch);
      await batch.commit();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف درس الفيديوهات: $e');
      rethrow;
    }
  }

  /// حذف بيانات درس الفيديوهات (مساعدة)
  Future<void> _deleteVideoLessonData(String lessonId, WriteBatch batch) async {
    // حذف الفيديوهات
    final videosSnapshot = await _firestore
        .collection('videos')
        .where('lessonId', isEqualTo: lessonId)
        .get();

    for (final videoDoc in videosSnapshot.docs) {
      batch.delete(videoDoc.reference);
      // حذف الملف المحلي إذا كان موجوداً
      await _deleteLocalVideoFile(videoDoc.id);
    }

    // حذف الدرس نفسه
    batch.delete(_firestore.collection('video_lessons').doc(lessonId));
  }

  // ===== إدارة الفيديوهات =====

  /// الحصول على فيديوهات درس معين
  Future<List<Video>> getVideos(String lessonId) async {
    try {
      final snapshot = await _firestore
          .collection('videos')
          .where('lessonId', isEqualTo: lessonId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => Video.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الفيديوهات: $e');
      return [];
    }
  }

  /// إضافة فيديو جديد مع تشفير الرابط
  Future<String> addVideo(Video video, String originalUrl) async {
    try {
      // تشفير رابط الفيديو
      final deviceId = await DeviceService.instance.getDeviceId();
      final encryptedData = _encryption.encryptVideoUrl(
        originalUrl,
        video.id,
        deviceId,
      );
      final encryptedUrl = encryptedData['encryptedData']!;

      final videoWithEncryption = video.copyWith(encryptedUrl: encryptedUrl);

      final docRef = await _firestore
          .collection('videos')
          .add(videoWithEncryption.toFirestore());

      notifyListeners();
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إضافة الفيديو: $e');
      rethrow;
    }
  }

  /// تحديث فيديو
  Future<void> updateVideo(Video video, {String? newOriginalUrl}) async {
    try {
      Video updatedVideo = video;

      // إذا تم تحديث الرابط، قم بتشفيره
      if (newOriginalUrl != null) {
        final deviceId = await DeviceService.instance.getDeviceId();
        final encryptedData = _encryption.encryptVideoUrl(
          newOriginalUrl,
          video.id,
          deviceId,
        );
        final encryptedUrl = encryptedData['encryptedData']!;
        updatedVideo = video.copyWith(encryptedUrl: encryptedUrl);
      }

      await _firestore
          .collection('videos')
          .doc(video.id)
          .update(updatedVideo.toFirestore());

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الفيديو: $e');
      rethrow;
    }
  }

  /// حذف فيديو
  Future<void> deleteVideo(String videoId) async {
    try {
      await _firestore.collection('videos').doc(videoId).delete();
      await _deleteLocalVideoFile(videoId);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الفيديو: $e');
      rethrow;
    }
  }

  /// فك تشفير رابط الفيديو للمشاهدة
  Future<String?> decryptVideoUrl(String videoId, String encryptedUrl) async {
    try {
      final deviceId = await DeviceService.instance.getDeviceId();

      // محاولة فك التشفير
      final encryptedData = {
        'encryptedData': encryptedUrl,
        'hash': '', // سيتم تحديثه لاحقاً
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      final decryptedData = _encryption.decryptVideoUrl(
        encryptedData,
        deviceId,
      );

      if (decryptedData != null && decryptedData['videoId'] == videoId) {
        return decryptedData['url'] as String;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في فك تشفير رابط الفيديو: $e');
      return null;
    }
  }

  // ===== التحميل المحمي =====

  /// تحميل فيديو وحفظه مشفراً محلياً
  Future<bool> downloadVideo(
    Video video, {
    Function(double)? onProgress,
  }) async {
    try {
      if (_videosDirectory == null) {
        await _initializeVideosDirectory();
      }

      // فك تشفير الرابط
      final originalUrl = await decryptVideoUrl(video.id, video.encryptedUrl);
      if (originalUrl == null) {
        throw Exception('فشل في فك تشفير رابط الفيديو');
      }

      // إنشاء اسم ملف مشفر
      final deviceId = await DeviceService.instance.getDeviceId();
      final encryptedFileName = _encryption.generateEncryptedFileName(
        video.id,
        deviceId,
      );
      final filePath = '${_videosDirectory!.path}/$encryptedFileName';

      // تحميل الفيديو
      await _dio.download(
        originalUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1 && onProgress != null) {
            onProgress(received / total);
          }
        },
      );

      // قراءة الملف وتشفيره
      final file = File(filePath);
      final videoData = await file.readAsBytes();
      final encryptedData = _encryption.encryptVideoData(videoData);

      // حفظ البيانات المشفرة
      await file.writeAsBytes(encryptedData);

      // إنشاء hash للتحقق من السلامة
      final hash = _encryption.generateHash(video.id + deviceId);

      // تحديث معلومات الفيديو في قاعدة البيانات المحلية
      await _updateLocalVideoInfo(video.id, filePath, hash);

      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل الفيديو: $e');
      return false;
    }
  }

  /// قراءة فيديو محمل محلياً وفك تشفيره
  Future<Uint8List?> getLocalVideoData(String videoId) async {
    try {
      final localInfo = await _getLocalVideoInfo(videoId);
      if (localInfo == null) return null;

      final file = File(localInfo['path']);
      if (!await file.exists()) return null;

      // قراءة البيانات المشفرة
      final encryptedData = await file.readAsBytes();

      // فك التشفير
      final decryptedData = _encryption.decryptVideoData(encryptedData);

      return decryptedData;
    } catch (e) {
      debugPrint('خطأ في قراءة الفيديو المحلي: $e');
      return null;
    }
  }

  /// حذف ملف فيديو محلي
  Future<void> _deleteLocalVideoFile(String videoId) async {
    try {
      final localInfo = await _getLocalVideoInfo(videoId);
      if (localInfo != null) {
        final file = File(localInfo['path']);
        if (await file.exists()) {
          await file.delete();
        }
        await _removeLocalVideoInfo(videoId);
      }
    } catch (e) {
      debugPrint('خطأ في حذف الملف المحلي: $e');
    }
  }

  /// تحديث معلومات الفيديو المحلي (مؤقت - يجب استخدام قاعدة بيانات محلية)
  Future<void> _updateLocalVideoInfo(
    String videoId,
    String path,
    String hash,
  ) async {
    // هنا يجب حفظ المعلومات في قاعدة بيانات محلية مشفرة
    // مؤقتاً سنستخدم SharedPreferences
    debugPrint('تحديث معلومات الفيديو المحلي: $videoId');
  }

  /// الحصول على معلومات الفيديو المحلي
  Future<Map<String, String>?> _getLocalVideoInfo(String videoId) async {
    // هنا يجب قراءة المعلومات من قاعدة بيانات محلية مشفرة
    // مؤقتاً سنعيد null
    return null;
  }

  /// إزالة معلومات الفيديو المحلي
  Future<void> _removeLocalVideoInfo(String videoId) async {
    // هنا يجب حذف المعلومات من قاعدة بيانات محلية مشفرة
    debugPrint('إزالة معلومات الفيديو المحلي: $videoId');
  }

  /// التحقق من وجود فيديو محمل محلياً
  Future<bool> isVideoDownloaded(String videoId) async {
    final localInfo = await _getLocalVideoInfo(videoId);
    if (localInfo == null) return false;

    final file = File(localInfo['path']);
    return await file.exists();
  }

  /// الحصول على حجم جميع الفيديوهات المحملة
  Future<int> getTotalDownloadedSize() async {
    try {
      if (_videosDirectory == null || !await _videosDirectory!.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await _videosDirectory!.list().toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('خطأ في حساب حجم الفيديوهات: $e');
      return 0;
    }
  }

  /// مسح جميع الفيديوهات المحملة
  Future<void> clearAllDownloadedVideos() async {
    try {
      if (_videosDirectory == null || !await _videosDirectory!.exists()) {
        return;
      }

      final files = await _videosDirectory!.list().toList();

      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }

      // مسح معلومات الفيديوهات المحلية
      // هنا يجب مسح قاعدة البيانات المحلية

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في مسح الفيديوهات: $e');
      rethrow;
    }
  }
}
