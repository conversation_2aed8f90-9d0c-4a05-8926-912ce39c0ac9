import 'package:flutter/material.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/color_picker_widget.dart';

/// صفحة إضافة/تعديل مادة الفيديوهات
class AddVideoSubjectPage extends StatefulWidget {
  final String sectionId;
  final VideoSubject? subject;

  const AddVideoSubjectPage({super.key, required this.sectionId, this.subject});

  @override
  State<AddVideoSubjectPage> createState() => _AddVideoSubjectPageState();
}

class _AddVideoSubjectPageState extends State<AddVideoSubjectPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();

  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;
  String _selectedColor = '#6C5CE7'; // اللون الافتراضي

  bool get _isEditing => widget.subject != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadSubjectData();
    }
  }

  void _loadSubjectData() {
    final subject = widget.subject!;
    _nameController.text = subject.name;
    _descriptionController.text = subject.description;
    _orderController.text = subject.order.toString();
    _isActive = subject.isActive;
    _selectedColor = subject.color;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  Future<void> _saveSubject() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final subject = VideoSubject(
        id: _isEditing ? widget.subject!.id : '',
        sectionId: widget.sectionId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        order: int.tryParse(_orderController.text) ?? 0,
        isActive: _isActive,
        createdAt: _isEditing ? widget.subject!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        createdByAdminId: 'admin_id', // TODO: استخدام معرف الأدمن الحقيقي
      );

      if (_isEditing) {
        await _videoService.updateVideoSubject(subject);
      } else {
        await _videoService.addVideoSubject(subject);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث المادة بنجاح' : 'تم إضافة المادة بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ المادة: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing
            ? 'تعديل مادة الفيديوهات'
            : 'إضافة مادة فيديوهات جديدة',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildSubjectInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSubjectInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.subject, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل مادة الفيديوهات' : 'مادة فيديوهات جديدة',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات مادة الفيديوهات'
                  : 'أدخل بيانات مادة الفيديوهات الجديدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _nameController,
          label: 'اسم المادة',
          hint: 'مثال: الرياضيات، الفيزياء، الكيمياء',
          prefixIcon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم المادة';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'وصف المادة (اختياري)',
          hint: 'وصف مختصر عن محتوى المادة',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _orderController,
          label: 'ترتيب المادة',
          hint: 'رقم ترتيب المادة (1، 2، 3...)',
          prefixIcon: Icons.sort,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال ترتيب المادة';
            }
            final order = int.tryParse(value);
            if (order == null || order < 0) {
              return 'يرجى إدخال رقم صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة المادة',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    _isActive
                        ? 'المادة نشطة ومرئية للطلاب'
                        : 'المادة غير نشطة ومخفية',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Colors.grey[300]!,
            textColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: _isEditing ? 'تحديث المادة' : 'إضافة المادة',
            onPressed: _saveSubject,
            backgroundColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}
