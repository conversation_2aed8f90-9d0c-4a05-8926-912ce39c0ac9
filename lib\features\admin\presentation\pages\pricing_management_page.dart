import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_service.dart';
import '../../../../shared/services/firebase_service.dart';
import '../../../../shared/models/subscription_price_model.dart';
import '../widgets/add_price_dialog.dart';
import '../widgets/edit_price_dialog.dart';

class PricingManagementPage extends StatefulWidget {
  const PricingManagementPage({super.key});

  @override
  State<PricingManagementPage> createState() => _PricingManagementPageState();
}

class _PricingManagementPageState extends State<PricingManagementPage> {
  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الأسعار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PricingService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة أسعار الاشتراك'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showAddPriceDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة سعر جديد',
          ),
          IconButton(
            onPressed: _createPricesForAllSubjects,
            icon: const Icon(Icons.auto_fix_high),
            tooltip: 'إنشاء أسعار تلقائية',
          ),
        ],
      ),
      body: Consumer<PricingService>(
        builder: (context, pricingService, child) {
          if (pricingService.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (pricingService.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64.sp,
                    color: AppTheme.errorColor,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'حدث خطأ في تحميل الأسعار',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    pricingService.error!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.errorColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: () => pricingService.refresh(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => pricingService.refresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات سريعة
                  _buildStatsCards(pricingService),
                  SizedBox(height: 24.h),

                  // قائمة الأسعار
                  _buildPricesList(pricingService),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatsCards(PricingService pricingService) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الأسعار',
            pricingService.subscriptionPrices.length.toString(),
            Icons.payments,
            AppTheme.primaryColor,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildStatCard(
            'العروض النشطة',
            pricingService.validSpecialPrices.length.toString(),
            Icons.local_offer,
            AppTheme.warningColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPricesList(PricingService pricingService) {
    final prices = pricingService.subscriptionPrices;

    if (prices.isEmpty) {
      return Center(
        child: Column(
          children: [
            SizedBox(height: 32.h),
            Icon(Icons.price_change_outlined, size: 64.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              'لا توجد أسعار محددة',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: Colors.grey),
            ),
            SizedBox(height: 8.h),
            Text(
              'اضغط على + لإضافة سعر جديد',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey),
            ),
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: _showAddPriceDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة سعر جديد'),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أسعار الاشتراكات (${prices.length})',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 16.h),
        ...prices.map((price) => _buildPriceCard(price)),
      ],
    );
  }

  Widget _buildPriceCard(SubscriptionPrice price) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(Icons.book, color: Colors.white, size: 24.sp),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    price.subjectName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'تم الإنشاء: ${_formatDate(price.createdAt)}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                  if (price.createdAt != price.updatedAt)
                    Text(
                      'آخر تحديث: ${_formatDate(price.updatedAt)}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  price.formattedPrice,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () => _showEditPriceDialog(price),
                      icon: const Icon(Icons.edit),
                      iconSize: 20.sp,
                      color: AppTheme.primaryColor,
                      tooltip: 'تعديل',
                    ),
                    IconButton(
                      onPressed: () => _confirmDeletePrice(price),
                      icon: const Icon(Icons.delete),
                      iconSize: 20.sp,
                      color: AppTheme.errorColor,
                      tooltip: 'حذف',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showAddPriceDialog() {
    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider.value(
        value: context.read<PricingService>(),
        child: const AddPriceDialog(),
      ),
    );
  }

  void _showEditPriceDialog(SubscriptionPrice price) {
    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider.value(
        value: context.read<PricingService>(),
        child: EditPriceDialog(price: price),
      ),
    );
  }

  void _confirmDeletePrice(SubscriptionPrice price) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف سعر "${price.subjectName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await context
                  .read<PricingService>()
                  .deleteSubscriptionPrice(price.id);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      success ? 'تم حذف السعر بنجاح' : 'فشل في حذف السعر',
                    ),
                    backgroundColor: success
                        ? AppTheme.successColor
                        : AppTheme.errorColor,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _createPricesForAllSubjects() async {
    try {
      // جلب جميع المواد
      final subjects = await FirebaseService.instance.getAllSubjects();

      if (subjects.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد مواد لإنشاء أسعار لها'),
              backgroundColor: AppTheme.warningColor,
            ),
          );
        }
        return;
      }

      // إظهار حوار لتحديد السعر الافتراضي
      final result = await showDialog<double>(
        context: context,
        builder: (context) => _buildDefaultPriceDialog(),
      );

      if (result != null) {
        final success = await context
            .read<PricingService>()
            .createPricesForSubjects(subjects, defaultPrice: result);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success ? 'تم إنشاء الأسعار بنجاح' : 'فشل في إنشاء الأسعار',
              ),
              backgroundColor: success
                  ? AppTheme.successColor
                  : AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Widget _buildDefaultPriceDialog() {
    final controller = TextEditingController(text: '10.0');

    return AlertDialog(
      title: const Text('إنشاء أسعار تلقائية'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('سيتم إنشاء أسعار للمواد التي لا تحتوي على أسعار'),
          SizedBox(height: 16.h),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'السعر الافتراضي (USD)',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () {
            final price = double.tryParse(controller.text);
            if (price != null && price > 0) {
              Navigator.of(context).pop(price);
            }
          },
          child: const Text('إنشاء'),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
