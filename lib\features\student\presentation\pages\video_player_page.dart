import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/encryption_service.dart';
import '../../../../shared/services/video_service.dart';

/// صفحة مشغل الفيديو مع دعم الجودات المتعددة والتحكم في السرعة
class VideoPlayerPage extends StatefulWidget {
  final Video video;

  const VideoPlayerPage({super.key, required this.video});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  VideoPlayerController? _controller;
  final VideoService _videoService = VideoService.instance;
  final EncryptionService _encryptionService = EncryptionService.instance;

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _showControls = true;
  bool _isFullscreen = false;

  VideoQuality _currentQuality = VideoQuality.auto;
  double _currentSpeed = 1.0;

  final List<double> _speedOptions = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _controller?.dispose();
    if (_isFullscreen) {
      _exitFullscreen();
    }
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // تحديد الجودة المناسبة
      if (_currentQuality == VideoQuality.auto) {
        _currentQuality = widget.video.bestAvailableQuality;
      }

      // الحصول على الرابط المشفر
      final encryptedUrl = widget.video.getEncryptedUrlForQuality(
        _currentQuality,
      );
      if (encryptedUrl == null || encryptedUrl.isEmpty) {
        throw Exception('لا يوجد رابط متاح لهذه الجودة');
      }

      // فك تشفير الرابط
      final decryptedUrl = _encryptionService.decryptText(encryptedUrl);

      // إنشاء مشغل الفيديو
      _controller = VideoPlayerController.networkUrl(Uri.parse(decryptedUrl));

      await _controller!.initialize();
      _controller!.setPlaybackSpeed(_currentSpeed);

      setState(() {
        _isLoading = false;
      });

      // بدء التشغيل التلقائي
      _controller!.play();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _isFullscreen
          ? null
          : AppBar(
              title: Text(
                widget.video.title,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              actions: [
                IconButton(
                  icon: const Icon(Icons.fullscreen),
                  onPressed: _toggleFullscreen,
                ),
              ],
            ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_controller == null || !_controller!.value.isInitialized) {
      return const Center(
        child: Text(
          'خطأ في تحميل الفيديو',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        children: [
          Center(
            child: AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: VideoPlayer(_controller!),
            ),
          ),
          if (_showControls) _buildControls(),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.white),
          SizedBox(height: 16.h),
          Text(
            _errorMessage,
            style: TextStyle(color: Colors.white, fontSize: 16.sp),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _initializePlayer,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          if (_isFullscreen) _buildTopControls(),
          const Spacer(),
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _exitFullscreen,
            ),
            Expanded(
              child: Text(
                widget.video.title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildQualityButton(),
            SizedBox(width: 8.w),
            _buildSpeedButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildProgressBar(),
            SizedBox(height: 16.h),
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    _controller!.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.white,
                    size: 32.sp,
                  ),
                  onPressed: _togglePlayPause,
                ),
                SizedBox(width: 16.w),
                Text(
                  _formatDuration(_controller!.value.position),
                  style: TextStyle(color: Colors.white, fontSize: 14.sp),
                ),
                const Spacer(),
                if (!_isFullscreen) ...[
                  _buildQualityButton(),
                  SizedBox(width: 8.w),
                  _buildSpeedButton(),
                  SizedBox(width: 8.w),
                ],
                Text(
                  _formatDuration(_controller!.value.duration),
                  style: TextStyle(color: Colors.white, fontSize: 14.sp),
                ),
                SizedBox(width: 16.w),
                IconButton(
                  icon: Icon(
                    _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                    color: Colors.white,
                  ),
                  onPressed: _toggleFullscreen,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return VideoProgressIndicator(
      _controller!,
      allowScrubbing: true,
      colors: const VideoProgressColors(
        playedColor: AppTheme.primaryColor,
        bufferedColor: Colors.grey,
        backgroundColor: Colors.white24,
      ),
    );
  }

  Widget _buildQualityButton() {
    return PopupMenuButton<VideoQuality>(
      icon: const Icon(Icons.high_quality, color: Colors.white),
      onSelected: _changeQuality,
      itemBuilder: (context) {
        return widget.video.availableQualities.map((quality) {
          return PopupMenuItem<VideoQuality>(
            value: quality,
            child: Row(
              children: [
                if (quality == _currentQuality)
                  const Icon(Icons.check, color: AppTheme.primaryColor),
                SizedBox(width: 8.w),
                Text(widget.video.getQualityDisplayText(quality)),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  Widget _buildSpeedButton() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.speed, color: Colors.white),
      onSelected: _changeSpeed,
      itemBuilder: (context) {
        return _speedOptions.map((speed) {
          return PopupMenuItem<double>(
            value: speed,
            child: Row(
              children: [
                if (speed == _currentSpeed)
                  const Icon(Icons.check, color: AppTheme.primaryColor),
                SizedBox(width: 8.w),
                Text('${speed}x'),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  void _togglePlayPause() {
    setState(() {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _toggleFullscreen() {
    if (_isFullscreen) {
      _exitFullscreen();
    } else {
      _enterFullscreen();
    }
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    setState(() {
      _isFullscreen = true;
    });
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    setState(() {
      _isFullscreen = false;
    });
  }

  void _changeQuality(VideoQuality quality) {
    if (quality != _currentQuality) {
      final currentPosition = _controller!.value.position;
      final wasPlaying = _controller!.value.isPlaying;

      setState(() {
        _currentQuality = quality;
      });

      _controller!.dispose();
      _initializePlayer().then((_) {
        if (!_hasError && _controller != null) {
          _controller!.seekTo(currentPosition);
          if (wasPlaying) {
            _controller!.play();
          }
        }
      });
    }
  }

  void _changeSpeed(double speed) {
    setState(() {
      _currentSpeed = speed;
      _controller!.setPlaybackSpeed(speed);
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }
}
