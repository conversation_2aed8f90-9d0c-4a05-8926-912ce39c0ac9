enum Flavor {
  student,
  admin,
}

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.student:
        return 'Smart Test - Student';
      case Flavor.admin:
        return 'Smart Test - Admin';
      default:
        return 'title';
    }
  }

  static String get appId {
    switch (appFlavor) {
      case Flavor.student:
        return 'com.smarttest.student';
      case Flavor.admin:
        return 'com.smarttest.admin';
      default:
        return 'com.smarttest.app';
    }
  }

  static bool get isStudent => appFlavor == Flavor.student;
  static bool get isAdmin => appFlavor == Flavor.admin;
}
