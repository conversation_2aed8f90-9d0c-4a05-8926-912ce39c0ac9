import 'package:cloud_firestore/cloud_firestore.dart';

class Section {
  final String id;
  final String name;
  final String description;
  final String color; // لون القسم
  final bool isActive;
  final bool isFree; // هل القسم مجاني أم مدفوع
  final DateTime createdAt;
  final DateTime updatedAt;

  Section({
    required this.id,
    required this.name,
    required this.description,
    this.color = '#6C5CE7', // لون افتراضي
    this.isActive = true,
    this.isFree = false, // افتراضياً مدفوع
    required this.createdAt,
    required this.updatedAt,
  });

  factory Section.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Section(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      isActive: data['isActive'] ?? true,
      isFree: data['isFree'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'color': color,
      'isActive': isActive,
      'isFree': isFree,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Section copyWith({
    String? id,
    String? name,
    String? description,
    String? color,
    bool? isActive,
    bool? isFree,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Section(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      isFree: isFree ?? this.isFree,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Section(id: $id, name: $name, description: $description, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Section && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
