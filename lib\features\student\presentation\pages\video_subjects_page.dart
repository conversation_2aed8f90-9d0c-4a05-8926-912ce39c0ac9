import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';

import 'video_units_page.dart';

/// صفحة مواد الفيديوهات للطالب
class VideoSubjectsPage extends StatefulWidget {
  final String sectionId;
  final bool hasSubscription;
  final bool isFreeSection; // هل القسم مجاني

  const VideoSubjectsPage({
    super.key,
    required this.sectionId,
    required this.hasSubscription,
    this.isFreeSection = false,
  });

  @override
  State<VideoSubjectsPage> createState() => _VideoSubjectsPageState();
}

class _VideoSubjectsPageState extends State<VideoSubjectsPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSubject> _subjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // تحميل بيانات الاشتراك أولاً
      await _subscriptionService.loadUserSubscription();

      final subjects = await _videoService.getVideoSubjects(widget.sectionId);

      setState(() {
        _subjects = subjects.where((subject) => subject.isActive).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المواد: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'المواد',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_subjects.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildSubjectsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.video_collection,
              color: Colors.white,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المواد',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر المادة لمشاهدة الفيديوهات',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectsList() {
    return ListView.builder(
      itemCount: _subjects.length,
      itemBuilder: (context, index) {
        final subject = _subjects[index];
        // إذا كان القسم مجاني، فجميع المواد متاحة
        // إذا كان القسم مدفوع، فيجب التحقق من الاشتراك
        final isAllowed =
            widget.isFreeSection ||
            (widget.hasSubscription &&
                _subscriptionService.isSubscribedToVideoSubject(subject.id));
        return _buildSubjectCard(subject, isAllowed);
      },
    );
  }

  Widget _buildSubjectCard(VideoSubject subject, bool isAllowed) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isAllowed
              ? () => _navigateToUnits(subject)
              : _showSubscriptionDialog,
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: isAllowed
                        ? _getSubjectColor(subject.color).withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.play_circle_filled,
                    color: isAllowed
                        ? _getSubjectColor(subject.color)
                        : Colors.grey,
                    size: 32.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subject.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: isAllowed
                              ? AppTheme.textPrimaryColor
                              : Colors.grey,
                        ),
                      ),
                      if (subject.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: isAllowed
                                ? AppTheme.textSecondaryColor
                                : Colors.grey,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  isAllowed ? Icons.arrow_forward_ios : Icons.lock,
                  color: isAllowed ? AppTheme.textSecondaryColor : Colors.grey,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_collection_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToUnits(VideoSubject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoUnitsPage(
          sectionId: widget.sectionId,
          subjectId: subject.id,
          hasSubscription: widget.hasSubscription,
          isFreeSection: widget.isFreeSection,
        ),
      ),
    );
  }

  void _showSubscriptionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اشتراك مطلوب'),
        content: const Text(
          'هذه المادة غير متاحة في اشتراكك الحالي. يرجى تفعيل اشتراك يشمل هذه المادة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Color _getSubjectColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }
}
