import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/student_data_service.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/content_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class WrongQuestionsPage extends StatefulWidget {
  final Subject subject;
  final bool isFreeAccess;

  const WrongQuestionsPage({
    super.key,
    required this.subject,
    this.isFreeAccess = false,
  });

  @override
  State<WrongQuestionsPage> createState() => _WrongQuestionsPageState();
}

class _WrongQuestionsPageState extends State<WrongQuestionsPage> {
  int _totalWrongQuestions = 0;
  int _wrongQuestionsByUnits = 0;
  int _wrongQuestionsByLessons = 0;

  Map<String, String> _unitsWithWrong = {}; // unitId -> unitName
  Map<String, String> _lessonsWithWrong = {}; // lessonId -> lessonName

  @override
  void initState() {
    super.initState();
    _loadWrongQuestionsCounts();
  }

  Future<void> _loadWrongQuestionsCounts() async {
    try {
      // تحميل قائمة الأسئلة الخاطئة
      final wrongQuestionIds = await StudentDataService.instance
          .getWrongQuestions();

      if (wrongQuestionIds.isEmpty) {
        setState(() {
          _totalWrongQuestions = 0;
          _wrongQuestionsByUnits = 0;
          _wrongQuestionsByLessons = 0;
          _unitsWithWrong.clear();
          _lessonsWithWrong.clear();
        });
        return;
      }

      // تحميل تفاصيل الأسئلة الخاطئة
      final questions = await ExamService.instance.getQuestionsByIds(
        wrongQuestionIds,
      );

      // تجميع الوحدات والدروس مع أسمائها الحقيقية
      final unitsMap = <String, String>{};
      final lessonsMap = <String, String>{};

      debugPrint(
        '🔍 [WrongQuestions] بدء تحميل أسماء الوحدات والدروس لـ ${questions.length} سؤال',
      );

      for (final question in questions) {
        if (question.unitId.isNotEmpty &&
            !unitsMap.containsKey(question.unitId)) {
          // الحصول على اسم الوحدة الحقيقي
          debugPrint(
            '📚 [WrongQuestions] تحميل اسم الوحدة: ${question.unitId}',
          );
          final unit = await ContentService.instance.getUnitById(
            question.unitId,
          );
          final unitName = unit?.name ?? 'وحدة غير معروفة';
          unitsMap[question.unitId] = unitName;
          debugPrint('✅ [WrongQuestions] تم تحميل اسم الوحدة: $unitName');
        }
        if (question.lessonId.isNotEmpty &&
            !lessonsMap.containsKey(question.lessonId)) {
          // الحصول على اسم الدرس الحقيقي
          debugPrint(
            '📖 [WrongQuestions] تحميل اسم الدرس: ${question.lessonId}',
          );
          final lesson = await ContentService.instance.getLessonById(
            question.lessonId,
          );
          final lessonName = lesson?.name ?? 'درس غير معروف';
          lessonsMap[question.lessonId] = lessonName;
          debugPrint('✅ [WrongQuestions] تم تحميل اسم الدرس: $lessonName');
        }
      }

      setState(() {
        _totalWrongQuestions = questions.length;
        _wrongQuestionsByUnits = unitsMap.length;
        _wrongQuestionsByLessons = lessonsMap.length;
        _unitsWithWrong = unitsMap;
        _lessonsWithWrong = lessonsMap;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل عدد الأسئلة الخاطئة: $e');
      setState(() {
        _totalWrongQuestions = 0;
        _wrongQuestionsByUnits = 0;
        _wrongQuestionsByLessons = 0;
        _unitsWithWrong.clear();
        _lessonsWithWrong.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadWrongQuestionsCounts,
      child: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          // رسالة توضيحية
          _buildInfoCard(),

          SizedBox(height: 16.h),

          // جميع الأسئلة الخاطئة
          _buildOptionCard(
            title: 'جميع الأسئلة الخاطئة',
            subtitle: 'جميع الأسئلة التي أجبت عليها خطأ',
            count: _totalWrongQuestions,
            icon: Icons.error,
            color: AppTheme.errorColor,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuestionsViewerPage(
                    title: 'الأسئلة الخاطئة',
                    subject: widget.subject,
                    questionType: QuestionFilterType.wrong,
                    isFreeAccess: widget.isFreeAccess,
                  ),
                ),
              );
            },
          ),

          SizedBox(height: 16.h),

          // الأسئلة الخاطئة حسب الوحدات
          _buildOptionCard(
            title: 'الخاطئة حسب الوحدات',
            subtitle: 'تصفح الأسئلة الخاطئة مقسمة حسب الوحدات',
            count: _wrongQuestionsByUnits,
            icon: Icons.folder_special,
            color: AppTheme.primaryColor,
            onTap: () {
              _showUnitsSelection();
            },
          ),

          SizedBox(height: 16.h),

          // الأسئلة الخاطئة حسب الدروس
          _buildOptionCard(
            title: 'الخاطئة حسب الدروس',
            subtitle: 'تصفح الأسئلة الخاطئة مقسمة حسب الدروس',
            count: _wrongQuestionsByLessons,
            icon: Icons.play_lesson,
            color: AppTheme.secondaryColor,
            onTap: () {
              _showLessonsSelection();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.errorColor.withValues(alpha: 0.1),
              AppTheme.errorColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: AppTheme.errorColor, size: 24.sp),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'الأسئلة التي تجيب عليها خطأ تُضاف هنا تلقائياً، وتختفي عند الإجابة الصحيحة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required int count,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(icon, color: Colors.white, size: 30.sp),
              ),

              SizedBox(width: 16.w),

              // المحتوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(Icons.quiz_outlined, size: 16.sp, color: color),
                        SizedBox(width: 4.w),
                        Text(
                          '$count سؤال',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondaryColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUnitsSelection() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر الوحدة',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.h),
            if (_unitsWithWrong.isEmpty)
              Text(
                'لا توجد وحدات تحتوي على أسئلة خاطئة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              )
            else
              ...(_unitsWithWrong.entries.map(
                (entry) => ListTile(
                  leading: Icon(Icons.folder, color: AppTheme.primaryColor),
                  title: Text(entry.value),
                  trailing: Icon(Icons.arrow_forward_ios, size: 16.sp),
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToUnitQuestions(entry.key, entry.value);
                  },
                ),
              )),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  void _showLessonsSelection() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر الدرس',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.h),
            if (_lessonsWithWrong.isEmpty)
              Text(
                'لا توجد دروس تحتوي على أسئلة خاطئة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              )
            else
              ...(_lessonsWithWrong.entries.map(
                (entry) => ListTile(
                  leading: Icon(Icons.book, color: AppTheme.secondaryColor),
                  title: Text(entry.value),
                  trailing: Icon(Icons.arrow_forward_ios, size: 16.sp),
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToLessonQuestions(entry.key, entry.value);
                  },
                ),
              )),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  void _navigateToUnitQuestions(String unitId, String unitName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionsViewerPage(
          subject: widget.subject,
          questionType: QuestionFilterType.wrong,
          unitId: unitId,
          title: 'الأسئلة الخاطئة - $unitName',
          isFreeAccess: widget.isFreeAccess,
        ),
      ),
    );
  }

  void _navigateToLessonQuestions(String lessonId, String lessonName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionsViewerPage(
          subject: widget.subject,
          questionType: QuestionFilterType.wrong,
          lessonId: lessonId,
          title: 'الأسئلة الخاطئة - $lessonName',
          isFreeAccess: widget.isFreeAccess,
        ),
      ),
    );
  }
}
