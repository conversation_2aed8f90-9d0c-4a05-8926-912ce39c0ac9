import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/services/content_service.dart';
import 'questions_viewer_page.dart';

class SubjectUnitsPage extends StatefulWidget {
  final Subject subject;
  final bool isFreeAccess;

  const SubjectUnitsPage({
    super.key,
    required this.subject,
    this.isFreeAccess = false,
  });

  @override
  State<SubjectUnitsPage> createState() => _SubjectUnitsPageState();
}

class _SubjectUnitsPageState extends State<SubjectUnitsPage> {
  List<Unit> _units = [];
  final Map<String, int> _unitQuestionCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUnits();
  }

  Future<void> _loadUnits() async {
    try {
      final units = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      // تحميل عدد الأسئلة لكل وحدة
      for (final unit in units) {
        // هنا سنحتاج دالة لحساب عدد الأسئلة في الوحدة
        _unitQuestionCounts[unit.id] = 0; // مؤقتاً
      }

      setState(() {
        _units = units;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_units.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadUnits,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _units.length,
        itemBuilder: (context, index) {
          final unit = _units[index];
          final questionCount = _unitQuestionCounts[unit.id] ?? 0;
          return _buildUnitCard(unit, questionCount);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد وحدات متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الوحدات قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitCard(Unit unit, int questionCount) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => QuestionsViewerPage(
                title: unit.name,
                subject: widget.subject,
                unitId: unit.id,
                questionType: QuestionFilterType.unit,
                isFreeAccess: widget.isFreeAccess,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                Color(
                  int.parse(unit.color.replaceFirst('#', '0xFF')),
                ).withValues(alpha: 0.1),
                Color(
                  int.parse(unit.color.replaceFirst('#', '0xFF')),
                ).withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة الوحدة
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: Color(int.parse(unit.color.replaceFirst('#', '0xFF'))),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(Icons.folder, color: Colors.white, size: 30.sp),
              ),

              SizedBox(width: 16.w),

              // معلومات الوحدة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      unit.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      unit.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.quiz_outlined,
                          size: 16.sp,
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '$questionCount سؤال',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: unit.isActive
                                ? AppTheme.successColor.withValues(alpha: 0.1)
                                : AppTheme.textSecondaryColor.withValues(
                                    alpha: 0.1,
                                  ),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            unit.isActive ? 'متاح' : 'مقفل',
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: unit.isActive
                                  ? AppTheme.successColor
                                  : AppTheme.textSecondaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // سهم للدخول
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondaryColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// أنواع فلترة الأسئلة
enum QuestionFilterType {
  unit,
  lesson,
  favorite,
  wrong,
  course,
  courseByUnit,
  courseByLesson,
  single, // عرض سؤال واحد محدد
}
