import 'package:flutter/material.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';

/// صفحة إضافة/تعديل قسم الفيديوهات
class AddVideoSectionPage extends StatefulWidget {
  final VideoSection? section;

  const AddVideoSectionPage({
    super.key,
    this.section,
  });

  @override
  State<AddVideoSectionPage> createState() => _AddVideoSectionPageState();
}

class _AddVideoSectionPageState extends State<AddVideoSectionPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();
  
  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;

  bool get _isEditing => widget.section != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadSectionData();
    }
  }

  void _loadSectionData() {
    final section = widget.section!;
    _nameController.text = section.name;
    _descriptionController.text = section.description;
    _orderController.text = section.order.toString();
    _isActive = section.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  Future<void> _saveSection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final section = VideoSection(
        id: _isEditing ? widget.section!.id : '',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        order: int.tryParse(_orderController.text) ?? 0,
        isActive: _isActive,
        createdAt: _isEditing ? widget.section!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await _videoService.updateVideoSection(section);
      } else {
        await _videoService.addVideoSection(section);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'تم تحديث القسم بنجاح' : 'تم إضافة القسم بنجاح'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ القسم: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing ? 'تعديل قسم الفيديوهات' : 'إضافة قسم فيديوهات جديد',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildSectionInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.video_library,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل قسم الفيديوهات' : 'قسم فيديوهات جديد',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات قسم الفيديوهات'
                  : 'أدخل بيانات قسم الفيديوهات الجديد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _nameController,
          label: 'اسم القسم',
          hint: 'مثال: الصف التاسع، الصف الثاني عشر علمي',
          prefixIcon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم القسم';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'وصف القسم (اختياري)',
          hint: 'وصف مختصر عن محتوى القسم',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _orderController,
          label: 'ترتيب القسم',
          hint: 'رقم ترتيب القسم (1، 2، 3...)',
          prefixIcon: Icons.sort,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال ترتيب القسم';
            }
            final order = int.tryParse(value);
            if (order == null || order < 0) {
              return 'يرجى إدخال رقم صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة القسم',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _isActive ? 'القسم نشط ومرئي للطلاب' : 'القسم غير نشط ومخفي',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Colors.grey[300]!,
            textColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: _isEditing ? 'تحديث القسم' : 'إضافة القسم',
            onPressed: _saveSection,
            backgroundColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}
