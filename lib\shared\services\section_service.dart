import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../core/models/section.dart';

class SectionService extends ChangeNotifier {
  static final SectionService _instance = SectionService._internal();
  static SectionService get instance => _instance;
  SectionService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<Section> _sections = [];

  List<Section> get sections => _sections;
  List<Section> get activeSections =>
      _sections.where((s) => s.isActive).toList();

  /// الحصول على الأقسام المجانية النشطة فقط
  List<Section> get freeSections =>
      _sections.where((s) => s.isActive && s.isFree).toList();

  /// الحصول على الأقسام المدفوعة النشطة فقط
  List<Section> get paidSections =>
      _sections.where((s) => s.isActive && !s.isFree).toList();

  /// تحميل جميع الأقسام
  Future<void> loadSections() async {
    try {
      final querySnapshot = await _firestore
          .collection('sections')
          .orderBy('createdAt', descending: false)
          .get();

      _sections = querySnapshot.docs
          .map((doc) => Section.fromFirestore(doc))
          .toList();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بالمعرف
  Future<Section?> getSectionById(String sectionId) async {
    try {
      final doc = await _firestore.collection('sections').doc(sectionId).get();

      if (doc.exists && doc.data() != null) {
        return Section.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل القسم: $e');
      return null;
    }
  }

  /// إضافة قسم جديد
  Future<void> addSection(Section section) async {
    try {
      await _firestore
          .collection('sections')
          .doc(section.id)
          .set(section.toFirestore());
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في إضافة القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<void> updateSection(Section section) async {
    try {
      await _firestore
          .collection('sections')
          .doc(section.id)
          .update(section.copyWith(updatedAt: DateTime.now()).toFirestore());
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في تحديث القسم: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<void> deleteSection(String sectionId) async {
    try {
      await _firestore.collection('sections').doc(sectionId).delete();
      await loadSections(); // إعادة تحميل الأقسام
    } catch (e) {
      debugPrint('خطأ في حذف القسم: $e');
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل قسم
  Future<void> toggleSectionStatus(String sectionId) async {
    try {
      final section = _sections.firstWhere((s) => s.id == sectionId);
      final updatedSection = section.copyWith(
        isActive: !section.isActive,
        updatedAt: DateTime.now(),
      );

      await updateSection(updatedSection);
    } catch (e) {
      debugPrint('خطأ في تغيير حالة القسم: $e');
      rethrow;
    }
  }

  /// البحث في الأقسام
  List<Section> searchSections(String query) {
    if (query.isEmpty) return activeSections;

    return activeSections.where((section) {
      return section.name.toLowerCase().contains(query.toLowerCase()) ||
          section.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// الحصول على عدد المواد في كل قسم
  Future<Map<String, int>> getSectionSubjectCounts() async {
    try {
      final Map<String, int> counts = {};

      for (final section in _sections) {
        final querySnapshot = await _firestore
            .collection('subjects')
            .where('sectionId', isEqualTo: section.id)
            .where('isActive', isEqualTo: true)
            .get();

        counts[section.id] = querySnapshot.docs.length;
      }

      return counts;
    } catch (e) {
      debugPrint('خطأ في حساب عدد المواد: $e');
      return {};
    }
  }
}
