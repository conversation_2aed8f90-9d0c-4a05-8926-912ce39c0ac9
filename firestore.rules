rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد للمجموعات العامة (قراءة فقط للجميع)
    // Public collections (read-only for everyone)
    match /subjects/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /sections/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /exams/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /units/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /lessons/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /questions/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /courses/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /videos/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    // قواعد للإعدادات العامة
    // Public settings
    match /settings/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    match /pricing_messages/{document} {
      allow read: if true;
      allow write: if false; // Admin only through console
    }
    
    // قواعد لبيانات المستخدمين (للمستخدمين المسجلين فقط)
    // User data rules (for authenticated users only)
    match /user_statistics/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_favorites/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_notes/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if request.auth != null;
    }

    // قواعد للأكواد (قراءة وكتابة للمستخدمين المسجلين)
    // Codes rules (read and write for authenticated users)
    match /subscription_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }

    match /activation_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }
    
    // منع الوصول لأي مجموعات أخرى
    // Deny access to any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
