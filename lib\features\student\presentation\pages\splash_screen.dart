import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/widgets/animated_logo.dart';
import 'student_home_page.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  late AnimationController _particleController;

  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<Offset> _logoSlideAnimation;

  late Animation<double> _smartTextAnimation;
  late Animation<double> _testTextAnimation;
  late Animation<double> _textOpacityAnimation;

  late Animation<double> _backgroundAnimation;
  late Animation<double> _particleAnimation;

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
    _playIntroSound();
  }

  void _initializeAnimations() {
    // Logo animations
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _logoRotationAnimation = Tween<double>(begin: -0.5, end: 0.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutBack),
      ),
    );

    _logoOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeIn),
      ),
    );

    _logoSlideAnimation =
        Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _logoController,
            curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
          ),
        );

    // Text animations
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _smartTextAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOutBack),
      ),
    );

    _testTextAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeOutBack),
      ),
    );

    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    // Background animation
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    // Particle animation
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );
  }

  void _startAnimationSequence() async {
    // Start background animation immediately
    _backgroundController.forward();
    _particleController.repeat();

    // Wait a bit then start logo animation with sound
    await Future.delayed(const Duration(milliseconds: 300));
    _playLogoSound();
    _logoController.forward();

    // Start text animation after logo starts with sound
    await Future.delayed(const Duration(milliseconds: 800));
    _playTextSound();
    _textController.forward();

    // Play completion sound
    await Future.delayed(const Duration(milliseconds: 2000));
    _playCompletionSound();

    // Navigate to main screen after all animations
    await Future.delayed(const Duration(milliseconds: 1500));
    if (mounted) {
      _navigateToMain();
    }
  }

  void _playIntroSound() async {
    if (_soundEnabled) {
      try {
        // Play your custom Smart Test intro sound
        await _audioPlayer.play(AssetSource('sounds/smart_test.mp3'));
        await HapticFeedback.lightImpact();
        print('🔊 Playing Smart Test intro sound');
      } catch (e) {
        print('❌ Error playing intro sound: $e');
        // Fallback to system sound
        await HapticFeedback.lightImpact();
        await SystemSound.play(SystemSoundType.click);
      }
    }
  }

  void _playLogoSound() async {
    if (_soundEnabled) {
      try {
        // Play Smart Test sound for logo appearance
        await _audioPlayer.play(AssetSource('sounds/smart_test.mp3'));
        await HapticFeedback.mediumImpact();
        print('🔊 Playing logo sound');
      } catch (e) {
        print('❌ Error playing logo sound: $e');
        // Fallback to system sound
        await HapticFeedback.mediumImpact();
        await SystemSound.play(SystemSoundType.click);
      }
    }
  }

  void _playTextSound() async {
    if (_soundEnabled) {
      try {
        // Play Smart Test sound for text appearance
        await _audioPlayer.play(AssetSource('sounds/smart_test.mp3'));
        await HapticFeedback.lightImpact();
        print('🔊 Playing text sound');
      } catch (e) {
        print('❌ Error playing text sound: $e');
        // Fallback to system sound
        await HapticFeedback.lightImpact();
        await SystemSound.play(SystemSoundType.click);
      }
    }
  }

  void _playCompletionSound() async {
    if (_soundEnabled) {
      try {
        // Play Smart Test sound for completion
        await _audioPlayer.play(AssetSource('sounds/smart_test.mp3'));
        await HapticFeedback.heavyImpact();
        print('🔊 Playing completion sound');
      } catch (e) {
        print('❌ Error playing completion sound: $e');
        // Fallback to system sound
        await HapticFeedback.heavyImpact();
        await SystemSound.play(SystemSoundType.click);
      }
    }
  }

  void _navigateToMain() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const StudentHomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    _particleController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _logoController,
          _textController,
          _backgroundController,
          _particleController,
        ]),
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.lerp(
                    const Color(0xFF667eea),
                    const Color(0xFF764ba2),
                    _backgroundAnimation.value,
                  )!,
                  Color.lerp(
                    const Color(0xFF764ba2),
                    const Color(0xFFf093fb),
                    _backgroundAnimation.value,
                  )!,
                ],
              ),
            ),
            child: Stack(
              children: [
                // Animated particles
                ..._buildParticles(context),

                // Main content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Animated Logo
                      SlideTransition(
                        position: _logoSlideAnimation,
                        child: Transform.rotate(
                          angle: _logoRotationAnimation.value,
                          child: Transform.scale(
                            scale: _logoScaleAnimation.value,
                            child: Opacity(
                              opacity: _logoOpacityAnimation.value,
                              child: AnimatedLogo(size: 120.w),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 40.h),

                      // Smart Text
                      Transform.scale(
                        scale: _smartTextAnimation.value,
                        child: Opacity(
                          opacity: _textOpacityAnimation.value,
                          child: ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                              colors: [Colors.white, Color(0xFFF8F9FA)],
                            ).createShader(bounds),
                            child: Text(
                              'Smart',
                              style: TextStyle(
                                fontSize: 48.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 2,
                                shadows: [
                                  Shadow(
                                    offset: const Offset(0, 4),
                                    blurRadius: 8,
                                    color: Colors.black.withValues(alpha: 0.3),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // Test Text
                      Transform.scale(
                        scale: _testTextAnimation.value,
                        child: Opacity(
                          opacity: _textOpacityAnimation.value,
                          child: ShaderMask(
                            shaderCallback: (bounds) => const LinearGradient(
                              colors: [Colors.white, Color(0xFFF8F9FA)],
                            ).createShader(bounds),
                            child: Text(
                              'Test',
                              style: TextStyle(
                                fontSize: 48.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 2,
                                shadows: [
                                  Shadow(
                                    offset: const Offset(0, 4),
                                    blurRadius: 8,
                                    color: Colors.black.withValues(alpha: 0.3),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildParticles(BuildContext context) {
    List<Widget> particles = [];
    for (int i = 0; i < 20; i++) {
      particles.add(
        Positioned(
          left: (i * 50.0) % MediaQuery.of(context).size.width,
          top: (i * 80.0) % MediaQuery.of(context).size.height,
          child: Transform.translate(
            offset: Offset(
              50 * _particleAnimation.value * (i % 2 == 0 ? 1 : -1),
              30 * _particleAnimation.value,
            ),
            child: Opacity(
              opacity: (1 - _particleAnimation.value) * 0.6,
              child: Container(
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ),
      );
    }
    return particles;
  }
}
