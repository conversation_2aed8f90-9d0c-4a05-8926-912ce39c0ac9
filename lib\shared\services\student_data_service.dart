import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/question_model.dart';

/// خدمة لحفظ بيانات الطالب محلياً
class StudentDataService {
  static final StudentDataService _instance = StudentDataService._internal();
  static StudentDataService get instance => _instance;
  StudentDataService._internal();

  static const String _favoriteQuestionsKey = 'favorite_questions';
  static const String _wrongQuestionsKey = 'wrong_questions';
  static const String _questionNotesKey = 'question_notes';
  static const String _questionResultsKey = 'question_results';
  static const String _userAnswersKey = 'user_answers';

  /// حفظ سؤال في المفضلة
  Future<void> addToFavorites(String questionId) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavoriteQuestions();
    if (!favorites.contains(questionId)) {
      favorites.add(questionId);
      await prefs.setStringList(_favoriteQuestionsKey, favorites);
    }
  }

  /// إزالة سؤال من المفضلة
  Future<void> removeFromFavorites(String questionId) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavoriteQuestions();
    favorites.remove(questionId);
    await prefs.setStringList(_favoriteQuestionsKey, favorites);
  }

  /// الحصول على قائمة الأسئلة المفضلة
  Future<List<String>> getFavoriteQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_favoriteQuestionsKey) ?? [];
  }

  /// فحص ما إذا كان السؤال في المفضلة
  Future<bool> isFavorite(String questionId) async {
    final favorites = await getFavoriteQuestions();
    return favorites.contains(questionId);
  }

  /// حفظ سؤال في الأسئلة الخاطئة
  Future<void> addToWrongQuestions(String questionId) async {
    final prefs = await SharedPreferences.getInstance();
    final wrongQuestions = await getWrongQuestions();
    if (!wrongQuestions.contains(questionId)) {
      wrongQuestions.add(questionId);
      await prefs.setStringList(_wrongQuestionsKey, wrongQuestions);
    }
  }

  /// إزالة سؤال من الأسئلة الخاطئة (عند الإجابة الصحيحة)
  Future<void> removeFromWrongQuestions(String questionId) async {
    final prefs = await SharedPreferences.getInstance();
    final wrongQuestions = await getWrongQuestions();
    wrongQuestions.remove(questionId);
    await prefs.setStringList(_wrongQuestionsKey, wrongQuestions);
  }

  /// الحصول على قائمة الأسئلة الخاطئة
  Future<List<String>> getWrongQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_wrongQuestionsKey) ?? [];
  }

  /// فحص ما إذا كان السؤال في الأسئلة الخاطئة
  Future<bool> isWrongQuestion(String questionId) async {
    final wrongQuestions = await getWrongQuestions();
    return wrongQuestions.contains(questionId);
  }

  /// حفظ ملاحظة على سؤال
  Future<void> saveQuestionNote(String questionId, String note) async {
    final prefs = await SharedPreferences.getInstance();
    final notes = await getQuestionNotes();
    if (note.trim().isEmpty) {
      notes.remove(questionId);
    } else {
      notes[questionId] = note.trim();
    }
    await prefs.setString(_questionNotesKey, jsonEncode(notes));
  }

  /// الحصول على ملاحظة سؤال
  Future<String> getQuestionNote(String questionId) async {
    final notes = await getQuestionNotes();
    return notes[questionId] ?? '';
  }

  /// الحصول على جميع الملاحظات
  Future<Map<String, String>> getQuestionNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final notesJson = prefs.getString(_questionNotesKey) ?? '{}';
    final notesMap = jsonDecode(notesJson) as Map<String, dynamic>;
    return notesMap.map((key, value) => MapEntry(key, value.toString()));
  }

  /// حفظ نتيجة سؤال (صحيح/خاطئ)
  Future<void> saveQuestionResult(String questionId, bool isCorrect) async {
    final prefs = await SharedPreferences.getInstance();
    final results = await getQuestionResults();
    results[questionId] = isCorrect;
    await prefs.setString(_questionResultsKey, jsonEncode(results));

    // إضافة/إزالة من الأسئلة الخاطئة
    if (isCorrect) {
      await removeFromWrongQuestions(questionId);
    } else {
      await addToWrongQuestions(questionId);
    }
  }

  /// الحصول على نتيجة سؤال
  Future<bool?> getQuestionResult(String questionId) async {
    final results = await getQuestionResults();
    return results[questionId];
  }

  /// الحصول على جميع نتائج الأسئلة
  Future<Map<String, bool>> getQuestionResults() async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_questionResultsKey) ?? '{}';
    final resultsMap = jsonDecode(resultsJson) as Map<String, dynamic>;
    return resultsMap.map((key, value) => MapEntry(key, value as bool));
  }

  /// حفظ إجابات المستخدم
  Future<void> saveUserAnswers(String questionId, List<String> answers) async {
    final prefs = await SharedPreferences.getInstance();
    final userAnswers = await getUserAnswers();
    userAnswers[questionId] = answers;
    await prefs.setString(_userAnswersKey, jsonEncode(userAnswers));
  }

  /// الحصول على إجابات المستخدم لسؤال
  Future<List<String>> getUserAnswersForQuestion(String questionId) async {
    final userAnswers = await getUserAnswers();
    return userAnswers[questionId] ?? [];
  }

  /// الحصول على جميع إجابات المستخدم
  Future<Map<String, List<String>>> getUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = prefs.getString(_userAnswersKey) ?? '{}';
    final answersMap = jsonDecode(answersJson) as Map<String, dynamic>;
    return answersMap.map((key, value) => 
      MapEntry(key, List<String>.from(value as List)));
  }

  /// مسح جميع البيانات
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_favoriteQuestionsKey);
    await prefs.remove(_wrongQuestionsKey);
    await prefs.remove(_questionNotesKey);
    await prefs.remove(_questionResultsKey);
    await prefs.remove(_userAnswersKey);
  }

  /// الحصول على إحصائيات
  Future<Map<String, int>> getStatistics() async {
    final favorites = await getFavoriteQuestions();
    final wrongQuestions = await getWrongQuestions();
    final results = await getQuestionResults();
    
    final correctAnswers = results.values.where((result) => result).length;
    final totalAnswered = results.length;
    
    return {
      'favoriteCount': favorites.length,
      'wrongCount': wrongQuestions.length,
      'correctCount': correctAnswers,
      'totalAnswered': totalAnswered,
    };
  }

  /// تحميل الأسئلة المفضلة مع تفاصيلها
  Future<List<Question>> loadFavoriteQuestionsWithDetails() async {
    // هذه الدالة ستحتاج تطبيق مع ExamService
    // مؤقتاً نرجع قائمة فارغة
    return [];
  }

  /// تحميل الأسئلة الخاطئة مع تفاصيلها
  Future<List<Question>> loadWrongQuestionsWithDetails() async {
    // هذه الدالة ستحتاج تطبيق مع ExamService
    // مؤقتاً نرجع قائمة فارغة
    return [];
  }
}
