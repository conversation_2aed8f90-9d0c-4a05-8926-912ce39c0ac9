import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';

import 'video_subjects_page.dart';

/// صفحة الأقسام المجانية للطالب
class FreeVideoSectionsPage extends StatefulWidget {
  const FreeVideoSectionsPage({super.key});

  @override
  State<FreeVideoSectionsPage> createState() => _FreeVideoSectionsPageState();
}

class _FreeVideoSectionsPageState extends State<FreeVideoSectionsPage> {
  final VideoService _videoService = VideoService.instance;

  List<VideoSection> _freeSections = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFreeSections();
  }

  Future<void> _loadFreeSections() async {
    try {
      final freeSections = await _videoService.getFreeVideoSections();

      setState(() {
        _freeSections = freeSections;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الأقسام المجانية: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'تجربة التطبيق',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_freeSections.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // رسالة توضيحية
        Container(
          width: double.infinity,
          margin: EdgeInsets.all(16.w),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.green.shade700,
                size: 24.sp,
              ),
              SizedBox(height: 8.h),
              Text(
                'هنا ستجد عدد صغير من الأسئلة كعينة مجانية، لذا قم بتفعيل اشتراك لتستمتع بآلاف الأسئلة وبمزايا كثيرة متاحة في القسم المدفوع',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // قائمة الأقسام المجانية
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadFreeSections,
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              itemCount: _freeSections.length,
              itemBuilder: (context, index) {
                final section = _freeSections[index];
                return _buildFreeSectionCard(section);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80.sp,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أقسام مجانية متاحة حالياً',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'تحقق مرة أخرى لاحقاً',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFreeSectionCard(VideoSection section) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoSubjectsPage(
                sectionId: section.id,
                hasSubscription: true, // الأقسام المجانية متاحة للجميع
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [Colors.green.shade400, Colors.green.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة القسم
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.play_circle_filled,
                  color: Colors.white,
                  size: 32.sp,
                ),
              ),
              SizedBox(width: 16.w),

              // معلومات القسم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            section.name,
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        // شارة مجاني
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            'مجاني',
                            style: TextStyle(
                              fontSize: 10.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (section.description.isNotEmpty) ...[
                      SizedBox(height: 8.h),
                      Text(
                        section.description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // سهم الانتقال
              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 20.sp),
            ],
          ),
        ),
      ),
    );
  }
}
