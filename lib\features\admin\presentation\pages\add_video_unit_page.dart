import 'package:flutter/material.dart';
import '../../../../shared/models/video_unit_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';

/// صفحة إضافة/تعديل وحدة الفيديوهات
class AddVideoUnitPage extends StatefulWidget {
  final String subjectId;
  final VideoUnit? unit;

  const AddVideoUnitPage({super.key, required this.subjectId, this.unit});

  @override
  State<AddVideoUnitPage> createState() => _AddVideoUnitPageState();
}

class _AddVideoUnitPageState extends State<AddVideoUnitPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();

  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;

  bool get _isEditing => widget.unit != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadUnitData();
    }
  }

  void _loadUnitData() {
    final unit = widget.unit!;
    _nameController.text = unit.name;
    _descriptionController.text = unit.description;
    _orderController.text = unit.order.toString();
    _isActive = unit.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  Future<void> _saveUnit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final unit = VideoUnit(
        id: _isEditing ? widget.unit!.id : '',
        sectionId: _isEditing ? widget.unit!.sectionId : '',
        subjectId: widget.subjectId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        order: int.tryParse(_orderController.text) ?? 0,
        isActive: _isActive,
        createdAt: _isEditing ? widget.unit!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        createdByAdminId: 'admin_id', // TODO: استخدام معرف الأدمن الحقيقي
      );

      if (_isEditing) {
        await _videoService.updateVideoUnit(unit);
      } else {
        await _videoService.addVideoUnit(unit);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث الوحدة بنجاح' : 'تم إضافة الوحدة بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الوحدة: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing
            ? 'تعديل وحدة الفيديوهات'
            : 'إضافة وحدة فيديوهات جديدة',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildUnitInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildUnitInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.folder, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل وحدة الفيديوهات' : 'وحدة فيديوهات جديدة',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات وحدة الفيديوهات'
                  : 'أدخل بيانات وحدة الفيديوهات الجديدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _nameController,
          label: 'اسم الوحدة',
          hint: 'مثال: الوحدة الأولى، الجبر، الهندسة',
          prefixIcon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم الوحدة';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'وصف الوحدة (اختياري)',
          hint: 'وصف مختصر عن محتوى الوحدة',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _orderController,
          label: 'ترتيب الوحدة',
          hint: 'رقم ترتيب الوحدة (1، 2، 3...)',
          prefixIcon: Icons.sort,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال ترتيب الوحدة';
            }
            final order = int.tryParse(value);
            if (order == null || order < 0) {
              return 'يرجى إدخال رقم صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة الوحدة',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    _isActive
                        ? 'الوحدة نشطة ومرئية للطلاب'
                        : 'الوحدة غير نشطة ومخفية',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Colors.grey[300]!,
            textColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: _isEditing ? 'تحديث الوحدة' : 'إضافة الوحدة',
            onPressed: _saveUnit,
            backgroundColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}
