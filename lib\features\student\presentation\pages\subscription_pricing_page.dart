import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_message_service.dart';

class SubscriptionPricingPage extends StatefulWidget {
  const SubscriptionPricingPage({super.key});

  @override
  State<SubscriptionPricingPage> createState() =>
      _SubscriptionPricingPageState();
}

class _SubscriptionPricingPageState extends State<SubscriptionPricingPage> {
  @override
  void initState() {
    super.initState();
    // تهيئة خدمة رسالة الأسعار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PricingMessageService.instance.initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: PricingMessageService.instance,
      child: Consumer<PricingMessageService>(
        builder: (context, service, child) {
          if (service.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: () => service.refresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رسالة الأسعار
                  if (service.hasMessage) ...[
                    _buildPricingMessageCard(service.currentMessage!.message),
                    SizedBox(height: 32.h),
                  ] else ...[
                    _buildNoPricingMessageCard(),
                    SizedBox(height: 32.h),
                  ],

                  // قسم التواصل
                  _buildContactSection(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPricingMessageCard(String message) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              AppTheme.secondaryColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.price_check,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Text(
                    'أسعار المواد المتاحة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                message,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(height: 1.6, fontSize: 16.sp),
                textDirection: TextDirection.rtl,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoPricingMessageCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48.sp,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد معلومات أسعار متاحة حالياً',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'يرجى التواصل معنا للحصول على معلومات الأسعار',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.secondaryColor.withValues(alpha: 0.1),
              AppTheme.primaryColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    gradient: AppTheme.secondaryGradient,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.support_agent,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Text(
                    'التواصل معنا',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.secondaryColor,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              'لإعلامك بطرق الدفع للحصول على كود التفعيل، يرجى التواصل عبر:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(height: 1.5),
            ),
            SizedBox(height: 20.h),

            // زر التلغرام
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _openTelegram,
                icon: const Icon(Icons.telegram),
                label: const Text('تواصل معنا عبر تيليجرام'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0088CC),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 2,
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // زر الواتساب
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _openWhatsApp,
                icon: const Icon(Icons.chat),
                label: const Text('تواصل معنا عبر واتساب'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF25D366),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _openTelegram() async {
    final service = PricingMessageService.instance;
    final telegramUsername =
        service.currentMessage?.telegramUsername ?? 'Smart_Test1';

    // قائمة من الروابط المختلفة لتجربتها
    final telegramUrls = [
      'https://t.me/$telegramUsername', // رابط الويب (الأكثر موثوقية)
      'tg://resolve?domain=$telegramUsername', // رابط التطبيق المباشر
      'telegram://resolve?domain=$telegramUsername', // رابط بديل
      'intent://resolve?domain=$telegramUsername#Intent;package=org.telegram.messenger;scheme=tg;end', // Android Intent
    ];

    bool opened = false;

    for (String url in telegramUrls) {
      try {
        final uri = Uri.parse(url);
        print('🔗 محاولة فتح رابط التلغرام: $url');

        // جرب فتح الرابط مباشرة بدون فحص canLaunchUrl
        try {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('✅ تم فتح التلغرام بنجاح مع LaunchMode.externalApplication');
          opened = true;
          break;
        } catch (e1) {
          print('❌ فشل LaunchMode.externalApplication: $e1');
          // إذا فشل، جرب مع الوضع الافتراضي
          try {
            await launchUrl(uri, mode: LaunchMode.platformDefault);
            print('✅ تم فتح التلغرام بنجاح مع LaunchMode.platformDefault');
            opened = true;
            break;
          } catch (e2) {
            print('❌ فشل LaunchMode.platformDefault: $e2');
            // تجاهل الخطأ وجرب الرابط التالي
            continue;
          }
        }
      } catch (e) {
        print('❌ خطأ في تحليل الرابط: $e');
        // تجاهل الخطأ وجرب الرابط التالي
        continue;
      }
    }

    if (!opened && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح تيليجرام. يرجى التأكد من تثبيت التطبيق.'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _openWhatsApp() async {
    final service = PricingMessageService.instance;
    final whatsappNumber = service.currentMessage?.whatsappNumber ?? '';

    if (whatsappNumber.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('رقم الواتساب غير متوفر حالياً'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // قائمة من الروابط المختلفة لتجربتها
    final whatsappUrls = [
      'https://wa.me/$whatsappNumber', // رابط الويب (الأكثر موثوقية)
      'whatsapp://send?phone=$whatsappNumber', // رابط التطبيق المباشر
      'https://api.whatsapp.com/send?phone=$whatsappNumber', // رابط API
      'intent://send?phone=$whatsappNumber#Intent;package=com.whatsapp;scheme=whatsapp;end', // Android Intent
    ];

    bool opened = false;

    for (String url in whatsappUrls) {
      try {
        final uri = Uri.parse(url);
        print('🔗 محاولة فتح رابط الواتساب: $url');

        // جرب فتح الرابط مباشرة بدون فحص canLaunchUrl
        try {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('✅ تم فتح الواتساب بنجاح مع LaunchMode.externalApplication');
          opened = true;
          break;
        } catch (e1) {
          print('❌ فشل LaunchMode.externalApplication: $e1');
          // إذا فشل، جرب مع الوضع الافتراضي
          try {
            await launchUrl(uri, mode: LaunchMode.platformDefault);
            print('✅ تم فتح الواتساب بنجاح مع LaunchMode.platformDefault');
            opened = true;
            break;
          } catch (e2) {
            print('❌ فشل LaunchMode.platformDefault: $e2');
            // تجاهل الخطأ وجرب الرابط التالي
            continue;
          }
        }
      } catch (e) {
        print('❌ خطأ في تحليل الرابط: $e');
        // تجاهل الخطأ وجرب الرابط التالي
        continue;
      }
    }

    if (!opened && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح واتساب. يرجى التأكد من تثبيت التطبيق.'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
