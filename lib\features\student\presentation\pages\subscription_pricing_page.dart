import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_service.dart';
import '../../../../shared/models/subscription_price_model.dart';

class SubscriptionPricingPage extends StatefulWidget {
  const SubscriptionPricingPage({super.key});

  @override
  State<SubscriptionPricingPage> createState() =>
      _SubscriptionPricingPageState();
}

class _SubscriptionPricingPageState extends State<SubscriptionPricingPage> {
  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الأسعار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PricingService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PricingService>(
      builder: (context, pricingService, child) {
        if (pricingService.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (pricingService.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: AppTheme.errorColor,
                ),
                SizedBox(height: 16.h),
                Text(
                  'حدث خطأ في تحميل الأسعار',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: 8.h),
                Text(
                  pricingService.error!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: AppTheme.errorColor),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                ElevatedButton(
                  onPressed: () => pricingService.refresh(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العروض الخاصة
              if (pricingService.validSpecialPrices.isNotEmpty) ...[
                _buildSpecialOffersSection(pricingService.validSpecialPrices),
                SizedBox(height: 24.h),
              ],

              // أسعار الاشتراكات العادية
              _buildRegularPricesSection(pricingService.subscriptionPrices),

              SizedBox(height: 32.h),

              // قسم التواصل
              _buildContactSection(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSpecialOffersSection(List<SpecialPrice> specialPrices) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.local_offer, color: AppTheme.warningColor, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'العروض الخاصة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.warningColor,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ...specialPrices.map((offer) => _buildSpecialOfferCard(offer)),
      ],
    );
  }

  Widget _buildSpecialOfferCard(SpecialPrice offer) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.warningColor.withValues(alpha: 0.1),
            AppTheme.warningColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppTheme.warningColor.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    offer.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.warningColor,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.warningColor,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    'خصم ${offer.discountPercentage.toStringAsFixed(0)}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              offer.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Text('السعر: ', style: Theme.of(context).textTheme.bodyMedium),
                Text(
                  offer.formattedOriginalPrice,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  offer.formattedDiscountedPrice,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'العرض ساري حتى: ${_formatDate(offer.validUntil)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.warningColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegularPricesSection(List<SubscriptionPrice> prices) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.payments, color: AppTheme.primaryColor, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'أسعار الاشتراك',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        if (prices.isEmpty)
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.price_change_outlined,
                  size: 64.sp,
                  color: Colors.grey,
                ),
                SizedBox(height: 16.h),
                Text(
                  'لا توجد أسعار متاحة حالياً',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...prices.map((price) => _buildPriceCard(price)),
      ],
    );
  }

  Widget _buildPriceCard(SubscriptionPrice price) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(Icons.book, color: Colors.white, size: 24.sp),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    price.subjectName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'اشتراك شهري',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Text(
              price.formattedPrice,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.secondaryColor.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.contact_support,
            size: 48.sp,
            color: AppTheme.primaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'تواصل معنا لإعلامك بطرق الدفع والحصول على كود تفعيل اشتراك',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            'وذلك بالضغط على:',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _openTelegram,
            icon: Icon(Icons.telegram, size: 24.sp),
            label: Text(
              'تواصل عبر تلغرام',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0088CC), // لون تلغرام
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25.r),
              ),
              elevation: 4,
            ),
          ),
        ],
      ),
    );
  }

  void _openTelegram() async {
    const telegramUrl = 'https://t.me/Smart_Test1';
    const telegramAppUrl = 'tg://resolve?domain=Smart_Test1';

    try {
      // محاولة فتح تلغرام مباشرة
      bool launched = false;

      // أولاً: محاولة فتح التطبيق مباشرة
      try {
        launched = await launchUrl(
          Uri.parse(telegramAppUrl),
          mode: LaunchMode.externalApplication,
        );
      } catch (e) {
        launched = false;
      }

      // إذا فشل، محاولة فتح الرابط العادي
      if (!launched) {
        launched = await launchUrl(
          Uri.parse(telegramUrl),
          mode: LaunchMode.externalApplication,
        );
      }

      if (!launched) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('لا يمكن فتح تلغرام. تأكد من تثبيت التطبيق.'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
