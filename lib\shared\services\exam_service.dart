import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../core/utils/logger.dart';
import '../models/question_model.dart';
import '../models/exam_model.dart';
import '../models/exam_result_model.dart';

class ExamService extends ChangeNotifier {
  static final ExamService _instance = ExamService._internal();
  static ExamService get instance => _instance;
  ExamService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Questions Management
  Future<String> createQuestion(Question question) async {
    try {
      final docRef = _firestore.collection('questions').doc();
      final questionWithId = question.copyWith(id: docRef.id);

      // إضافة timeout للعملية
      await docRef
          .set(questionWithId.toMap())
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () =>
                throw Exception('انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى'),
          );

      Logger.success('تم إنشاء السؤال: ${questionWithId.id}');

      notifyListeners();
      return questionWithId.id;
    } catch (e) {
      Logger.error('خطأ في إنشاء السؤال', e);
      rethrow;
    }
  }

  Future<void> updateQuestion(Question question) async {
    try {
      await _firestore
          .collection('questions')
          .doc(question.id)
          .update(question.copyWith(updatedAt: DateTime.now()).toMap())
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () =>
                throw Exception('انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى'),
          );

      Logger.success('تم تحديث السؤال: ${question.id}');
      notifyListeners();
    } catch (e) {
      Logger.error('خطأ في تحديث السؤال', e);
      rethrow;
    }
  }

  Future<void> deleteQuestion(String questionId) async {
    try {
      await _firestore.collection('questions').doc(questionId).delete();
      Logger.success('تم حذف السؤال: $questionId');
      notifyListeners();
    } catch (e) {
      Logger.error('خطأ في حذف السؤال', e);
      rethrow;
    }
  }

  Future<Question?> getQuestion(String questionId) async {
    try {
      final doc = await _firestore
          .collection('questions')
          .doc(questionId)
          .get();
      if (doc.exists) {
        return Question.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      Logger.error('خطأ في جلب السؤال', e);
      return null;
    }
  }

  /// الحصول على سؤال بواسطة المعرف (alias للـ getQuestion)
  Future<Question?> getQuestionById(String questionId) async {
    return await getQuestion(questionId);
  }

  Stream<List<Question>> getQuestionsBySubject(String subjectId) {
    return _firestore
        .collection('questions')
        .where('subjectId', isEqualTo: subjectId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Question.fromMap(doc.data())).toList(),
        );
  }

  Stream<List<Question>> getAllQuestions() {
    return _firestore
        .collection('questions')
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Question.fromMap(doc.data())).toList(),
        );
  }

  /// تحميل أسئلة بمعرفات محددة (للأسئلة المفضلة والخاطئة)
  Future<List<Question>> getQuestionsByIds(List<String> questionIds) async {
    try {
      if (questionIds.isEmpty) return [];

      // Firebase لا يدعم استعلام 'in' لأكثر من 10 عناصر
      // لذا سنقسم القائمة إلى مجموعات
      List<Question> allQuestions = [];

      for (int i = 0; i < questionIds.length; i += 10) {
        final batch = questionIds.skip(i).take(10).toList();

        final snapshot = await _firestore
            .collection('questions')
            .where('id', whereIn: batch)
            .where('isActive', isEqualTo: true)
            .get();

        final questions = snapshot.docs
            .map((doc) => Question.fromMap(doc.data()))
            .toList();

        allQuestions.addAll(questions);
      }

      Logger.success(
        'تم تحميل ${allQuestions.length} سؤال من ${questionIds.length} معرف',
      );
      return allQuestions;
    } catch (e) {
      Logger.error('خطأ في تحميل الأسئلة بالمعرفات', e);
      return [];
    }
  }

  /// تحميل جميع أسئلة الدورات من جميع المواد
  Stream<List<Question>> getAllCourseQuestions() {
    return _firestore
        .collection('questions')
        .where('isCourseQuestion', isEqualTo: true)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Question.fromMap(doc.data())).toList(),
        );
  }

  // Exams Management
  Future<String> createExam(Exam exam) async {
    try {
      final docRef = _firestore.collection('exams').doc();
      final examWithId = exam.copyWith(id: docRef.id);

      await docRef.set(examWithId.toMap());
      Logger.success('تم إنشاء الاختبار: ${examWithId.id}');

      notifyListeners();
      return examWithId.id;
    } catch (e) {
      Logger.error('خطأ في إنشاء الاختبار', e);
      rethrow;
    }
  }

  Future<void> updateExam(Exam exam) async {
    try {
      await _firestore
          .collection('exams')
          .doc(exam.id)
          .update(exam.copyWith(updatedAt: DateTime.now()).toMap());

      Logger.success('تم تحديث الاختبار: ${exam.id}');
      notifyListeners();
    } catch (e) {
      Logger.error('خطأ في تحديث الاختبار', e);
      rethrow;
    }
  }

  Future<void> deleteExam(String examId) async {
    try {
      await _firestore.collection('exams').doc(examId).delete();
      Logger.success('تم حذف الاختبار: $examId');
      notifyListeners();
    } catch (e) {
      Logger.error('خطأ في حذف الاختبار', e);
      rethrow;
    }
  }

  Future<Exam?> getExam(String examId) async {
    try {
      final doc = await _firestore.collection('exams').doc(examId).get();
      if (doc.exists) {
        return Exam.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      Logger.error('خطأ في جلب الاختبار', e);
      return null;
    }
  }

  Stream<List<Exam>> getExamsBySubject(String subjectId) {
    return _firestore
        .collection('exams')
        .where('subjectId', isEqualTo: subjectId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Exam.fromMap(doc.data())).toList(),
        );
  }

  Stream<List<Exam>> getAvailableExams(String subjectId) {
    return _firestore
        .collection('exams')
        .where('subjectId', isEqualTo: subjectId)
        .where('status', whereIn: ['published', 'active'])
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Exam.fromMap(doc.data())).toList(),
        );
  }

  // Exam Results Management
  Future<String> startExam(
    String examId,
    String userId,
    String deviceId,
  ) async {
    try {
      final docRef = _firestore.collection('exam_results').doc();

      final examResult = ExamResult(
        id: docRef.id,
        examId: examId,
        userId: userId,
        deviceId: deviceId,
        answers: [],
        totalPoints: 0,
        earnedPoints: 0,
        percentage: 0.0,
        isPassed: false,
        status: ResultStatus.inProgress,
        startedAt: DateTime.now(),
        timeSpent: 0,
        attemptNumber: await _getNextAttemptNumber(examId, userId),
        metadata: {},
      );

      await docRef.set(examResult.toMap());
      Logger.success('تم بدء الاختبار: ${examResult.id}');

      return examResult.id;
    } catch (e) {
      Logger.error('خطأ في بدء الاختبار', e);
      rethrow;
    }
  }

  Future<void> submitExamResult(ExamResult result) async {
    try {
      await _firestore
          .collection('exam_results')
          .doc(result.id)
          .update(result.toMap());

      Logger.success('تم إرسال نتيجة الاختبار: ${result.id}');
      notifyListeners();
    } catch (e) {
      Logger.error('خطأ في إرسال نتيجة الاختبار', e);
      rethrow;
    }
  }

  Future<ExamResult?> getExamResult(String resultId) async {
    try {
      final doc = await _firestore
          .collection('exam_results')
          .doc(resultId)
          .get();
      if (doc.exists) {
        return ExamResult.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      Logger.error('خطأ في جلب نتيجة الاختبار', e);
      return null;
    }
  }

  Stream<List<ExamResult>> getUserExamResults(String userId) {
    return _firestore
        .collection('exam_results')
        .where('userId', isEqualTo: userId)
        .orderBy('startedAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => ExamResult.fromMap(doc.data()))
              .toList(),
        );
  }

  Future<int> _getNextAttemptNumber(String examId, String userId) async {
    try {
      final results = await _firestore
          .collection('exam_results')
          .where('examId', isEqualTo: examId)
          .where('userId', isEqualTo: userId)
          .get();

      return results.docs.length + 1;
    } catch (e) {
      Logger.error('خطأ في حساب رقم المحاولة', e);
      return 1;
    }
  }

  // Helper methods
  Future<List<Question>> getExamQuestions(Exam exam) async {
    try {
      final questions = <Question>[];
      for (String questionId in exam.questionIds) {
        final question = await getQuestion(questionId);
        if (question != null) {
          questions.add(question);
        }
      }
      return questions;
    } catch (e) {
      Logger.error('خطأ في جلب أسئلة الاختبار', e);
      return [];
    }
  }

  Future<Map<String, dynamic>> getExamStatistics(String examId) async {
    try {
      final results = await _firestore
          .collection('exam_results')
          .where('examId', isEqualTo: examId)
          .where('status', isEqualTo: 'completed')
          .get();

      if (results.docs.isEmpty) {
        return {
          'totalAttempts': 0,
          'averageScore': 0.0,
          'passRate': 0.0,
          'highestScore': 0.0,
          'lowestScore': 0.0,
        };
      }

      final scores = results.docs
          .map((doc) => ExamResult.fromMap(doc.data()).percentage)
          .toList();

      final passedCount = results.docs
          .where((doc) => ExamResult.fromMap(doc.data()).isPassed)
          .length;

      return {
        'totalAttempts': results.docs.length,
        'averageScore': scores.reduce((a, b) => a + b) / scores.length,
        'passRate': (passedCount / results.docs.length) * 100,
        'highestScore': scores.reduce((a, b) => a > b ? a : b),
        'lowestScore': scores.reduce((a, b) => a < b ? a : b),
      };
    } catch (e) {
      Logger.error('خطأ في جلب إحصائيات الاختبار', e);
      return {};
    }
  }

  /// الحصول على أسئلة الوحدة
  Future<List<Question>> getQuestionsByUnit(
    String unitId,
    bool isCourseQuestion,
  ) async {
    try {
      debugPrint('البحث عن أسئلة الوحدة: $unitId, دورة: $isCourseQuestion');

      final querySnapshot = await _firestore
          .collection('questions')
          .where('unitId', isEqualTo: unitId)
          .where('isCourseQuestion', isEqualTo: isCourseQuestion)
          .where('isActive', isEqualTo: true)
          .get();

      debugPrint('تم العثور على ${querySnapshot.docs.length} مستند');

      final questions = querySnapshot.docs
          .map((doc) {
            try {
              final question = Question.fromMap(doc.data());
              final preview = question.questionText.length > 30
                  ? question.questionText.substring(0, 30) + '...'
                  : question.questionText;
              debugPrint('تم تحويل السؤال: ${question.id} - $preview');
              return question;
            } catch (e) {
              debugPrint('خطأ في تحويل السؤال ${doc.id}: $e');
              return null;
            }
          })
          .where((question) => question != null)
          .cast<Question>()
          .toList();

      debugPrint('تم تحميل ${questions.length} سؤال بنجاح للوحدة $unitId');
      return questions;
    } catch (e) {
      debugPrint('خطأ في تحميل أسئلة الوحدة: $e');
      return [];
    }
  }

  /// الحصول على أسئلة الدرس
  Future<List<Question>> getQuestionsByLesson(
    String lessonId,
    bool isCourseQuestion,
  ) async {
    try {
      debugPrint('البحث عن أسئلة الدرس: $lessonId, دورة: $isCourseQuestion');

      final querySnapshot = await _firestore
          .collection('questions')
          .where('lessonId', isEqualTo: lessonId)
          .where('isCourseQuestion', isEqualTo: isCourseQuestion)
          .where('isActive', isEqualTo: true)
          .get();

      debugPrint('تم العثور على ${querySnapshot.docs.length} مستند للدرس');

      final questions = querySnapshot.docs
          .map((doc) {
            try {
              final question = Question.fromMap(doc.data());
              final preview = question.questionText.length > 30
                  ? '${question.questionText.substring(0, 30)}...'
                  : question.questionText;
              debugPrint('تم تحويل السؤال: ${question.id} - $preview');
              return question;
            } catch (e) {
              debugPrint('خطأ في تحويل السؤال ${doc.id}: $e');
              return null;
            }
          })
          .where((question) => question != null)
          .cast<Question>()
          .toList();

      debugPrint('تم تحميل ${questions.length} سؤال بنجاح للدرس $lessonId');
      return questions;
    } catch (e) {
      debugPrint('خطأ في تحميل أسئلة الدرس: $e');
      return [];
    }
  }

  /// البحث في الأسئلة
  Future<List<Question>> searchQuestions(String query, String subjectId) async {
    try {
      debugPrint('🔍 البحث عن: "$query" في المادة: $subjectId');

      final querySnapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      final questions = querySnapshot.docs
          .map((doc) => Question.fromMap(doc.data()))
          .toList();

      debugPrint('📊 تم تحميل ${questions.length} سؤال للبحث');

      // فلترة النتائج محلياً - البحث في نص السؤال والشرح والإجابات
      final searchResults = questions.where((question) {
        final searchQuery = query.toLowerCase().trim();

        // البحث في نص السؤال
        final questionTextMatch = question.questionText.toLowerCase().contains(
          searchQuery,
        );

        // البحث في الشرح
        final explanationMatch = question.explanation.toLowerCase().contains(
          searchQuery,
        );

        // البحث في الإجابات
        final optionsMatch = question.options.any(
          (option) => option.toLowerCase().contains(searchQuery),
        );

        return questionTextMatch || explanationMatch || optionsMatch;
      }).toList();

      debugPrint('✅ تم العثور على ${searchResults.length} نتيجة بحث');
      return searchResults;
    } catch (e) {
      debugPrint('❌ خطأ في البحث في الأسئلة: $e');
      return [];
    }
  }

  /// تحميل أسئلة الدورات لمادة معينة
  Future<List<Question>> getCourseQuestionsBySubject(String subjectId) async {
    try {
      debugPrint('🔍 تحميل أسئلة الدورات للمادة: $subjectId');

      final querySnapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .where('isCourseQuestion', isEqualTo: true)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      debugPrint('📊 تم العثور على ${querySnapshot.docs.length} سؤال دورة');

      final questions = querySnapshot.docs
          .map((doc) {
            try {
              final question = Question.fromMap(doc.data());
              return question;
            } catch (e) {
              debugPrint('خطأ في تحويل السؤال ${doc.id}: $e');
              return null;
            }
          })
          .where((question) => question != null)
          .cast<Question>()
          .toList();

      debugPrint(
        '✅ تم تحميل ${questions.length} سؤال دورة بنجاح للمادة $subjectId',
      );
      return questions;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أسئلة الدورات للمادة: $e');
      return [];
    }
  }
}
