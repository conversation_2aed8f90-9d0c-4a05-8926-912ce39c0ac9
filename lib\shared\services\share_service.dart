import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../models/question_model.dart';
import '../models/subject_model.dart';
import '../../core/utils/logger.dart';

/// خدمة مشاركة الأسئلة
class ShareService {
  static final ShareService _instance = ShareService._internal();
  static ShareService get instance => _instance;
  ShareService._internal();

  /// مشاركة ID السؤال
  Future<void> shareQuestion(
    BuildContext context,
    Question question,
    Subject subject,
  ) async {
    try {
      Logger.info('مشاركة السؤال: ${question.id}');

      // إنشاء نص المشاركة المباشر
      final shareText = _buildShareText(question, subject);

      // المشاركة المباشرة
      await _shareGeneral(shareText);
    } catch (e) {
      Logger.error('خطأ في مشاركة السؤال: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, 'حدث خطأ في مشاركة السؤال');
      }
    }
  }

  /// بناء نص المشاركة المباشر
  String _buildShareText(Question question, Subject subject) {
    final questionPreview = question.questionText.length > 100
        ? '${question.questionText.substring(0, 100)}...'
        : question.questionText;

    return '''
🎓 سؤال من تطبيق Smart Test

📚 المادة: ${subject.name}
❓ السؤال: $questionPreview

📱 لعرض السؤال كاملاً:
1. حمل تطبيق Smart Test ان لم يكن موجودا لديك.
2. انسخ 🆔 السؤال الموجود في الأسفل
3. اذهب للصفحة الرئيسية واضغط على زر 🔍
4. الصق 🆔 السؤال هناك واضغط "بحث"


🆔 السؤال:

${question.id}''';
  }

  /// المشاركة العامة
  Future<void> _shareGeneral(String text) async {
    try {
      await Share.share(text);
      Logger.info('تم مشاركة السؤال بنجاح');
    } catch (e) {
      Logger.error('خطأ في المشاركة العامة: $e');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
