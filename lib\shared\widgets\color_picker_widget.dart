import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget لاختيار الألوان
class ColorPickerWidget extends StatelessWidget {
  final String selectedColor;
  final Function(String) onColorSelected;
  final String title;

  const ColorPickerWidget({
    super.key,
    required this.selectedColor,
    required this.onColorSelected,
    this.title = 'اختر لون',
  });

  // قائمة الألوان المتاحة
  static const List<String> availableColors = [
    '#6C5CE7', // بنفسجي (افتراضي)
    '#2196F3', // أزرق
    '#4CAF50', // أخضر
    '#FF9800', // برتقالي
    '#F44336', // أحمر
    '#9C27B0', // بنفسجي غامق
    '#00BCD4', // سماوي
    '#8BC34A', // أخضر فاتح
    '#FFC107', // أصفر
    '#795548', // بني
    '#607D8B', // رمادي مزرق
    '#E91E63', // وردي
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: availableColors.map((color) {
              final isSelected = selectedColor == color;
              return GestureDetector(
                onTap: () => onColorSelected(color),
                child: Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.black : Colors.grey[300]!,
                      width: isSelected ? 3 : 1,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20.sp,
                        )
                      : null,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

/// Helper function لتحويل اللون من String إلى Color
Color colorFromString(String colorString) {
  return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
}

/// Helper function لتحويل اللون من Color إلى String
String colorToString(Color color) {
  return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
}
