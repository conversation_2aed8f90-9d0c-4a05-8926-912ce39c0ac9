import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/subscription_service.dart';

class SubjectSelectionWidget extends StatelessWidget {
  final List<Subject> allSubjects;
  final List<Subject> subscribedSubjects;
  final SubscriptionService subscriptionService;

  const SubjectSelectionWidget({
    super.key,
    required this.allSubjects,
    required this.subscribedSubjects,
    required this.subscriptionService,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildStatsCard(context),

          SizedBox(height: 24.h),

          // المواد المشترك بها
          if (subscribedSubjects.isNotEmpty) ...[
            _buildSectionHeader(
              'المواد المفعلة',
              subscribedSubjects.length,
              Icons.verified,
              AppTheme.successColor,
            ),
            SizedBox(height: 16.h),
            _buildSubjectsList(subscribedSubjects, isSubscribed: true),
            SizedBox(height: 24.h),
          ],

          // المواد غير المشترك بها
          _buildSectionHeader(
            'المواد المتاحة',
            allSubjects.length - subscribedSubjects.length,
            Icons.lock_outline,
            AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          _buildSubjectsList(
            allSubjects
                .where(
                  (subject) =>
                      !subscribedSubjects.any((sub) => sub.id == subject.id),
                )
                .toList(),
            isSubscribed: false,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context) {
    final totalSubjects = allSubjects.length;
    final subscribedCount = subscribedSubjects.length;
    final progress = totalSubjects > 0 ? subscribedCount / totalSubjects : 0.0;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              AppTheme.secondaryColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي المواد',
                    totalSubjects.toString(),
                    Icons.book_outlined,
                    AppTheme.primaryColor,
                  ),
                ),
                Container(width: 1, height: 40.h, color: AppTheme.dividerColor),
                Expanded(
                  child: _buildStatItem(
                    'المواد المفعلة',
                    subscribedCount.toString(),
                    Icons.verified,
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),

            SizedBox(height: 20.h),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'نسبة التفعيل',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: AppTheme.dividerColor,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progress == 1.0
                        ? AppTheme.successColor
                        : AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSectionHeader(
    String title,
    int count,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: color, size: 20.sp),
        ),
        SizedBox(width: 12.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        SizedBox(width: 8.w),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubjectsList(
    List<Subject> subjects, {
    required bool isSubscribed,
  }) {
    if (subjects.isEmpty) {
      return _buildEmptyState(isSubscribed);
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: subjects.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final subject = subjects[index];
        return _buildSubjectCard(subject, isSubscribed);
      },
    );
  }

  Widget _buildSubjectCard(Subject subject, bool isSubscribed) {
    final activationDate = isSubscribed
        ? subscriptionService.getSubjectActivationDate(subject.id)
        : null;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: isSubscribed
              ? Border.all(
                  color: AppTheme.successColor.withValues(alpha: 0.3),
                  width: 2,
                )
              : null,
        ),
        child: Row(
          children: [
            // أيقونة المادة
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: isSubscribed
                    ? AppTheme.successColor.withValues(alpha: 0.1)
                    : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                _getSubjectIcon(subject.name),
                color: isSubscribed
                    ? AppTheme.successColor
                    : AppTheme.textSecondaryColor,
                size: 24.sp,
              ),
            ),

            SizedBox(width: 16.w),

            // معلومات المادة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subject.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  if (subject.description.isNotEmpty) ...[
                    SizedBox(height: 4.h),
                    Text(
                      subject.description,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                  if (isSubscribed && activationDate != null) ...[
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 14.sp,
                          color: AppTheme.successColor,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'مفعل منذ ${_formatDate(activationDate)}',
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: AppTheme.successColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // حالة المادة
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: isSubscribed
                    ? AppTheme.successColor.withValues(alpha: 0.1)
                    : AppTheme.errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSubscribed ? Icons.check_circle : Icons.lock,
                    size: 14.sp,
                    color: isSubscribed
                        ? AppTheme.successColor
                        : AppTheme.errorColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    isSubscribed ? 'مفعل' : 'مقفل',
                    style: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w600,
                      color: isSubscribed
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isSubscribed) {
    return Container(
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          Icon(
            isSubscribed ? Icons.celebration : Icons.lock_outline,
            size: 64.sp,
            color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            isSubscribed ? 'لا توجد مواد مفعلة بعد' : 'جميع المواد مفعلة!',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isSubscribed
                ? 'استخدم كود التفعيل لإضافة مواد جديدة'
                : 'تهانينا! لديك إمكانية الوصول لجميع المواد',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getSubjectIcon(String subjectName) {
    final name = subjectName.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return Icons.calculate;
    }
    if (name.contains('فيزياء') || name.contains('physics')) {
      return Icons.science;
    }
    if (name.contains('كيمياء') || name.contains('chemistry')) {
      return Icons.biotech;
    }
    if (name.contains('أحياء') || name.contains('biology')) {
      return Icons.eco;
    }
    if (name.contains('عربية') || name.contains('arabic')) {
      return Icons.language;
    }
    if (name.contains('إنجليزية') || name.contains('english')) {
      return Icons.translate;
    }
    if (name.contains('تاريخ') || name.contains('history')) {
      return Icons.history_edu;
    }
    if (name.contains('جغرافيا') || name.contains('geography')) {
      return Icons.public;
    }
    return Icons.book;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) return 'اليوم';
    if (difference == 1) return 'أمس';
    if (difference < 7) return '$difference أيام';
    if (difference < 30) return '${(difference / 7).floor()} أسابيع';
    return '${date.day}/${date.month}/${date.year}';
  }
}
