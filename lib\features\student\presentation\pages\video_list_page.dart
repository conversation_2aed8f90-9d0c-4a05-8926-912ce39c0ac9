import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_service.dart';
import 'video_player_page.dart';

/// صفحة قائمة الفيديوهات للطالب
class VideoListPage extends StatefulWidget {
  final String sectionId;
  final String subjectId;
  final String unitId;
  final String lessonId;
  final bool hasSubscription;

  const VideoListPage({
    super.key,
    required this.sectionId,
    required this.subjectId,
    required this.unitId,
    required this.lessonId,
    required this.hasSubscription,
  });

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage> {
  final VideoService _videoService = VideoService.instance;

  List<Video> _videos = [];
  bool _isLoading = true;
  Map<String, List<VideoQuality>> _downloadedQualities = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final videos = await _videoService.getVideos(widget.lessonId);
      final activeVideos = videos.where((video) => video.isActive).toList();

      // تحميل معلومات الفيديوهات المحملة
      final downloadedQualities = <String, List<VideoQuality>>{};
      for (final video in activeVideos) {
        final qualities = await _videoService.getDownloadedQualities(video.id);
        downloadedQualities[video.id] = qualities;
        if (qualities.isNotEmpty) {
          debugPrint(
            'فيديو محمل: ${video.title} - الجودات: ${qualities.map((q) => q.name).join(', ')}',
          );
        }
      }

      setState(() {
        _videos = activeVideos;
        _downloadedQualities = downloadedQualities;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الفيديوهات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الفيديوهات',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_videos.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildVideosList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.video_collection,
              color: Colors.white,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر الفيديو للمشاهدة',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosList() {
    return ListView.builder(
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  Widget _buildVideoCard(Video video) {
    final canAccess = widget.hasSubscription || video.isPreview;
    final downloadedQualities = _downloadedQualities[video.id] ?? [];
    final hasDownloaded = downloadedQualities.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canAccess
              ? () => _playVideo(video)
              : () => _showSubscriptionDialog(),
          borderRadius: BorderRadius.circular(16.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Stack(
                  children: [
                    Container(
                      width: 80.w,
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.play_circle_filled,
                        color: hasDownloaded
                            ? Colors.green
                            : AppTheme.primaryColor,
                        size: 32.sp,
                      ),
                    ),
                    if (!canAccess)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Icon(
                            Icons.lock,
                            color: Colors.white,
                            size: 24.sp,
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              video.title,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: canAccess
                                    ? AppTheme.textPrimaryColor
                                    : AppTheme.textSecondaryColor,
                              ),
                            ),
                          ),
                          if (video.isPreview)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Text(
                                'مجاني',
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      if (video.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          video.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            video.formattedDuration,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          SizedBox(width: 16.w),
                          if (video.availableQualities.isNotEmpty) ...[
                            Icon(
                              Icons.high_quality,
                              size: 16.sp,
                              color: AppTheme.textSecondaryColor,
                            ),
                            SizedBox(width: 4.w),
                            Expanded(
                              child: Text(
                                video.availableQualities
                                    .map((q) => video.getQualityDisplayText(q))
                                    .join(', '),
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                if (canAccess) ...[
                  // زر التحميل
                  IconButton(
                    onPressed: () => _showDownloadDialog(video),
                    icon: Icon(
                      Icons.download,
                      color: AppTheme.primaryColor,
                      size: 20.sp,
                    ),
                    padding: EdgeInsets.all(8.w),
                    constraints: BoxConstraints(
                      minWidth: 32.w,
                      minHeight: 32.h,
                    ),
                  ),
                  SizedBox(width: 8.w),
                ],
                Icon(
                  canAccess ? Icons.play_arrow : Icons.lock,
                  color: canAccess
                      ? AppTheme.primaryColor
                      : AppTheme.textSecondaryColor,
                  size: 24.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_collection_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الفيديوهات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _playVideo(Video video) async {
    // التحقق من وجود فيديو محمل
    final downloadedQualities = _downloadedQualities[video.id];
    VideoQuality? selectedQuality;
    bool isOffline = false;

    if (downloadedQualities != null && downloadedQualities.isNotEmpty) {
      // إذا كان هناك جودة واحدة فقط، استخدمها مباشرة
      if (downloadedQualities.length == 1) {
        selectedQuality = downloadedQualities.first;
        isOffline = true;
      } else {
        // إذا كان هناك عدة جودات، اعرض قائمة للاختيار
        selectedQuality = await _showPlaybackQualityDialog(
          video,
          downloadedQualities,
        );
        if (selectedQuality != null) {
          isOffline = true;
        }
      }
    }

    // إذا لم يتم اختيار جودة محملة، استخدم التشغيل عبر الإنترنت
    if (selectedQuality == null) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideoPlayerPage(video: video),
          ),
        );
      }
    } else {
      // تشغيل الفيديو المحمل
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideoPlayerPage(
              video: video,
              preferredQuality: selectedQuality,
              isOfflineMode: true,
            ),
          ),
        );
      }
    }
  }

  /// عرض قائمة اختيار الجودة للتشغيل
  Future<VideoQuality?> _showPlaybackQualityDialog(
    Video video,
    List<VideoQuality> downloadedQualities,
  ) async {
    return showDialog<VideoQuality>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر جودة التشغيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ...downloadedQualities.map(
              (quality) => ListTile(
                leading: const Icon(Icons.offline_pin, color: Colors.green),
                title: Text('${video.getQualityDisplayText(quality)} (محمل)'),
                onTap: () => Navigator.pop(context, quality),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.cloud, color: Colors.blue),
              title: const Text('تشغيل عبر الإنترنت'),
              onTap: () => Navigator.pop(context, null),
            ),
          ],
        ),
      ),
    );
  }

  void _showSubscriptionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اشتراك مطلوب'),
        content: const Text('يجب تفعيل الاشتراك لمشاهدة هذا الفيديو'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showDownloadDialog(Video video) {
    final downloadedQualities = _downloadedQualities[video.id] ?? [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تحميل الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              video.title,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12.h),
            Text(
              'اختر جودة التحميل:',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            ...video.availableQualities.map((quality) {
              final isDownloaded = downloadedQualities.contains(quality);
              return ListTile(
                title: Text(video.getQualityDisplayText(quality)),
                leading: Icon(
                  isDownloaded ? Icons.check_circle : Icons.video_file,
                  color: isDownloaded ? Colors.green : null,
                ),
                trailing: isDownloaded
                    ? Text('محمل', style: TextStyle(color: Colors.green))
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  if (isDownloaded) {
                    _showAlreadyDownloadedMessage();
                  } else {
                    _downloadVideo(video, quality);
                  }
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showAlreadyDownloadedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('هذا الفيديو تم تحميله مسبقا'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Future<void> _downloadVideo(Video video, VideoQuality quality) async {
    try {
      // عرض حوار التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text('جاري التحميل...'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16.h),
              Text('يتم تحميل الفيديو، يرجى الانتظار...'),
            ],
          ),
        ),
      );

      // تحميل الفيديو
      final success = await _videoService.downloadVideo(
        video,
        quality,
        onProgress: (progress) {
          // يمكن تحديث شريط التقدم هنا
        },
      );

      // إغلاق حوار التحميل
      if (mounted) Navigator.pop(context);

      // عرض نتيجة التحميل وتحديث القائمة
      if (mounted) {
        if (success) {
          // تحديث قائمة الفيديوهات المحملة
          final updatedQualities = await _videoService.getDownloadedQualities(
            video.id,
          );
          setState(() {
            _downloadedQualities[video.id] = updatedQualities;
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'تم تحميل الفيديو بنجاح' : 'فشل في تحميل الفيديو',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      // إغلاق حوار التحميل في حالة الخطأ
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
