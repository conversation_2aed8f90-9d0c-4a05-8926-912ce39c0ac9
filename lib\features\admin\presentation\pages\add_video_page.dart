import 'package:flutter/material.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';

/// صفحة إضافة/تعديل الفيديو
class AddVideoPage extends StatefulWidget {
  final String lessonId;
  final Video? video;

  const AddVideoPage({
    super.key,
    required this.lessonId,
    this.video,
  });

  @override
  State<AddVideoPage> createState() => _AddVideoPageState();
}

class _AddVideoPageState extends State<AddVideoPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _urlController = TextEditingController();
  final _orderController = TextEditingController();
  final _durationController = TextEditingController();
  
  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;
  String _selectedQuality = '720p';

  final List<String> _qualityOptions = ['360p', '480p', '720p', '1080p'];

  bool get _isEditing => widget.video != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadVideoData();
    }
  }

  void _loadVideoData() {
    final video = widget.video!;
    _titleController.text = video.title;
    _descriptionController.text = video.description;
    _urlController.text = video.originalUrl;
    _orderController.text = video.order.toString();
    _durationController.text = video.duration.toString();
    _selectedQuality = video.quality;
    _isActive = video.isActive;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _urlController.dispose();
    _orderController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  Future<void> _saveVideo() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final video = Video(
        id: _isEditing ? widget.video!.id : '',
        sectionId: _isEditing ? widget.video!.sectionId : '',
        subjectId: _isEditing ? widget.video!.subjectId : '',
        unitId: _isEditing ? widget.video!.unitId : '',
        lessonId: widget.lessonId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        originalUrl: _urlController.text.trim(),
        encryptedUrl: _isEditing ? widget.video!.encryptedUrl : '',
        thumbnailUrl: _isEditing ? widget.video!.thumbnailUrl : '',
        duration: int.tryParse(_durationController.text) ?? 0,
        quality: _selectedQuality,
        fileSize: _isEditing ? widget.video!.fileSize : 0,
        order: int.tryParse(_orderController.text) ?? 0,
        downloadStatus: _isEditing ? widget.video!.downloadStatus : DownloadStatus.notDownloaded,
        localPath: _isEditing ? widget.video!.localPath : '',
        isActive: _isActive,
        createdAt: _isEditing ? widget.video!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await _videoService.updateVideo(video);
      } else {
        await _videoService.addVideo(video);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'تم تحديث الفيديو بنجاح' : 'تم إضافة الفيديو بنجاح'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ الفيديو: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing ? 'تعديل الفيديو' : 'إضافة فيديو جديد',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildVideoInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildVideoInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.video_library,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل الفيديو' : 'فيديو جديد',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات الفيديو'
                  : 'أدخل بيانات الفيديو الجديد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _titleController,
          label: 'عنوان الفيديو',
          hint: 'مثال: شرح الدرس الأول، حل التمارين',
          prefixIcon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال عنوان الفيديو';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'وصف الفيديو (اختياري)',
          hint: 'وصف مختصر عن محتوى الفيديو',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _urlController,
          label: 'رابط الفيديو (Google Drive)',
          hint: 'https://drive.google.com/file/d/...',
          prefixIcon: Icons.link,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال رابط الفيديو';
            }
            if (!value.contains('drive.google.com')) {
              return 'يرجى إدخال رابط Google Drive صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _orderController,
                label: 'ترتيب الفيديو',
                hint: '1، 2، 3...',
                prefixIcon: Icons.sort,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال ترتيب الفيديو';
                  }
                  final order = int.tryParse(value);
                  if (order == null || order < 0) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _durationController,
                label: 'مدة الفيديو (ثانية)',
                hint: '300، 600، 1200...',
                prefixIcon: Icons.access_time,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال مدة الفيديو';
                  }
                  final duration = int.tryParse(value);
                  if (duration == null || duration <= 0) {
                    return 'يرجى إدخال مدة صحيحة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildQualityDropdown(),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildQualityDropdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'جودة الفيديو',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedQuality,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.high_quality),
              ),
              items: _qualityOptions.map((quality) {
                return DropdownMenuItem(
                  value: quality,
                  child: Text(quality),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedQuality = value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة الفيديو',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _isActive ? 'الفيديو نشط ومرئي للطلاب' : 'الفيديو غير نشط ومخفي',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Colors.grey[300]!,
            textColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: _isEditing ? 'تحديث الفيديو' : 'إضافة الفيديو',
            onPressed: _saveVideo,
            backgroundColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}
