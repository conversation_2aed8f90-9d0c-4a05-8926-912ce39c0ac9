import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/services/section_service.dart';

class SectionsManagementPage extends StatefulWidget {
  const SectionsManagementPage({super.key});

  @override
  State<SectionsManagementPage> createState() => _SectionsManagementPageState();
}

class _SectionsManagementPageState extends State<SectionsManagementPage> {
  List<Section> _sections = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSections();
  }

  Future<void> _loadSections() async {
    try {
      setState(() => _isLoading = true);
      await SectionService.instance.loadSections();
      _sections = SectionService.instance.sections;
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الأقسام: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة الأقسام',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: _showAddSectionDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _sections.isEmpty
          ? _buildEmptyState()
          : RefreshIndicator(
              onRefresh: _loadSections,
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: _sections.length,
                itemBuilder: (context, index) {
                  final section = _sections[index];
                  return _buildSectionCard(section);
                },
              ),
            ),
    );
  }

  Widget _buildSectionCard(Section section) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // أيقونة القسم
                Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor,
                        AppTheme.primaryColor.withOpacity(0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(Icons.school, color: Colors.white, size: 24.w),
                ),

                SizedBox(width: 16.w),

                // معلومات القسم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              section.name,
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                          ),

                          // حالة القسم
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: section.isActive
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              section.isActive ? 'نشط' : 'غير نشط',
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: section.isActive
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),

                      if (section.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          section.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showEditSectionDialog(section),
                    icon: Icon(Icons.edit, size: 16.w),
                    label: Text('تعديل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                ElevatedButton.icon(
                  onPressed: () => _toggleSectionStatus(section),
                  icon: Icon(
                    section.isActive ? Icons.visibility_off : Icons.visibility,
                    size: 16.w,
                  ),
                  label: Text(section.isActive ? 'إخفاء' : 'إظهار'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: section.isActive
                        ? Colors.orange
                        : Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      vertical: 8.h,
                      horizontal: 12.w,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                ElevatedButton.icon(
                  onPressed: () => _deleteSectionConfirm(section),
                  icon: Icon(Icons.delete, size: 16.w),
                  label: Text('حذف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      vertical: 8.h,
                      horizontal: 12.w,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أقسام',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على + لإضافة قسم جديد',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _showAddSectionDialog() {
    _showSectionDialog();
  }

  void _showEditSectionDialog(Section section) {
    _showSectionDialog(section: section);
  }

  void _showSectionDialog({Section? section}) {
    final nameController = TextEditingController(text: section?.name ?? '');
    final descriptionController = TextEditingController(
      text: section?.description ?? '',
    );
    bool isActive = section?.isActive ?? true;
    bool isFree = section?.isFree ?? false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(section == null ? 'إضافة قسم جديد' : 'تعديل القسم'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم القسم',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'وصف القسم',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Text('نشط: '),
                      Switch(
                        value: isActive,
                        onChanged: (value) =>
                            setDialogState(() => isActive = value),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Icon(
                        isFree ? Icons.lock_open : Icons.lock,
                        color: isFree ? Colors.green : Colors.orange,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isFree ? 'قسم مجاني' : 'قسم مدفوع',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              isFree
                                  ? 'سيظهر في صفحة تجربة التطبيق'
                                  : 'سيظهر في الصفحة الرئيسية',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: isFree,
                        onChanged: (value) =>
                            setDialogState(() => isFree = value),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _saveSection(
                section,
                nameController.text,
                descriptionController.text,
                isActive,
                isFree,
              ),
              child: Text(section == null ? 'إضافة' : 'حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveSection(
    Section? existingSection,
    String name,
    String description,
    bool isActive,
    bool isFree,
  ) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال اسم القسم')));
      return;
    }

    try {
      final now = DateTime.now();
      final section =
          existingSection?.copyWith(
            name: name.trim(),
            description: description.trim(),
            isActive: isActive,
            isFree: isFree,
            updatedAt: now,
          ) ??
          Section(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            name: name.trim(),
            description: description.trim(),
            isActive: isActive,
            isFree: isFree,
            createdAt: now,
            updatedAt: now,
          );

      if (existingSection == null) {
        await SectionService.instance.addSection(section);
      } else {
        await SectionService.instance.updateSection(section);
      }

      if (mounted) {
        Navigator.pop(context);
      }
      await _loadSections();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              existingSection == null
                  ? 'تم إضافة القسم بنجاح'
                  : 'تم تحديث القسم بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ القسم: $e')));
      }
    }
  }

  Future<void> _toggleSectionStatus(Section section) async {
    try {
      await SectionService.instance.toggleSectionStatus(section.id);
      await _loadSections();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              section.isActive ? 'تم إخفاء القسم' : 'تم إظهار القسم',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تغيير حالة القسم: $e')));
      }
    }
  }

  void _deleteSectionConfirm(Section section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القسم "${section.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteSection(section);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSection(Section section) async {
    try {
      await SectionService.instance.deleteSection(section.id);
      await _loadSections();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم حذف القسم بنجاح')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حذف القسم: $e')));
      }
    }
  }
}
