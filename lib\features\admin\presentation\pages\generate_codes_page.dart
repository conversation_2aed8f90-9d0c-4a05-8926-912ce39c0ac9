import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subscription_code_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/video_service.dart';
import '../widgets/generated_codes_display_widget.dart';

class GenerateCodesPage extends StatefulWidget {
  final VoidCallback? onCodesGenerated;

  const GenerateCodesPage({super.key, this.onCodesGenerated});

  @override
  State<GenerateCodesPage> createState() => _GenerateCodesPageState();
}

class _GenerateCodesPageState extends State<GenerateCodesPage> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController(text: '10');
  final _durationController = TextEditingController(text: '30');
  final _prefixController = TextEditingController();

  bool _isLoading = false;
  int _codeLength = 8;
  bool _includeNumbers = true;
  bool _includeLetters = true;
  bool _includeSpecialChars = false;

  // إعدادات المواد
  List<Subject> _allSubjects = [];
  List<String> _selectedSubjectIds = [];
  bool _isAllSubjects = false;
  bool _isLoadingSubjects = true;

  // إعدادات الفيديوهات
  List<VideoSubject> _allVideoSubjects = [];
  List<String> _selectedVideoSubjectIds = [];
  bool _isAllVideoSubjects = false;
  bool _isLoadingVideoSubjects = true;

  @override
  void initState() {
    super.initState();
    _loadSubjects();
    _loadVideoSubjects();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _durationController.dispose();
    _prefixController.dispose();
    super.dispose();
  }

  Future<void> _loadSubjects() async {
    try {
      final subjects = await ContentService.instance.getAllSubjects();
      setState(() {
        _allSubjects = subjects;
        _isLoadingSubjects = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSubjects = false;
      });
    }
  }

  Future<void> _loadVideoSubjects() async {
    try {
      final videoService = VideoService.instance;
      final sections = await videoService.getVideoSections();
      final List<VideoSubject> allVideoSubjects = [];

      for (final section in sections) {
        final subjects = await videoService.getVideoSubjects(section.id);
        allVideoSubjects.addAll(subjects);
      }

      setState(() {
        _allVideoSubjects = allVideoSubjects;
        _isLoadingVideoSubjects = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingVideoSubjects = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إنشاء أكواد التفعيل',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.successGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إعدادات الكمية والمدة
              _buildQuantityAndDurationSection(),

              SizedBox(height: 24.h),

              // إعدادات الكود
              _buildCodeSettingsSection(),

              SizedBox(height: 24.h),

              // اختيار المواد
              _buildSubjectSelectionSection(),

              SizedBox(height: 24.h),

              // اختيار الفيديوهات
              _buildVideoSubjectSelectionSection(),

              SizedBox(height: 24.h),

              // معاينة الكود
              _buildPreviewSection(),

              SizedBox(height: 32.h),

              // أزرار الإجراءات
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuantityAndDurationSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الكمية والمدة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            Row(
              children: [
                // عدد الأكواد
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'عدد الأكواد *',
                      border: OutlineInputBorder(),
                      suffixText: 'كود',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final quantity = int.tryParse(value);
                      if (quantity == null || quantity < 1 || quantity > 1000) {
                        return 'من 1 إلى 1000';
                      }
                      return null;
                    },
                  ),
                ),

                SizedBox(width: 16.w),

                // مدة الصلاحية
                Expanded(
                  child: TextFormField(
                    controller: _durationController,
                    decoration: const InputDecoration(
                      labelText: 'مدة الصلاحية *',
                      border: OutlineInputBorder(),
                      suffixText: 'يوم',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final duration = int.tryParse(value);
                      if (duration == null || duration < 1 || duration > 365) {
                        return 'من 1 إلى 365';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeSettingsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الكود',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // بادئة الكود
            TextFormField(
              controller: _prefixController,
              decoration: const InputDecoration(
                labelText: 'بادئة الكود (اختياري)',
                border: OutlineInputBorder(),
                hintText: 'مثل: ST2024',
              ),
              validator: (value) {
                if (value != null && value.isNotEmpty && value.length > 6) {
                  return 'أقصى 6 أحرف';
                }
                return null;
              },
            ),

            SizedBox(height: 16.h),

            // طول الكود
            Row(
              children: [
                Text(
                  'طول الكود: ',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Expanded(
                  child: Slider(
                    value: _codeLength.toDouble(),
                    min: 6,
                    max: 12,
                    divisions: 6,
                    label: '$_codeLength أحرف',
                    onChanged: (value) {
                      setState(() {
                        _codeLength = value.round();
                      });
                    },
                  ),
                ),
                Text(
                  '$_codeLength',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // خيارات الأحرف
            Text('نوع الأحرف:', style: Theme.of(context).textTheme.titleMedium),

            SizedBox(height: 8.h),

            CheckboxListTile(
              title: const Text('أرقام (0-9)'),
              value: _includeNumbers,
              onChanged: (value) {
                setState(() {
                  _includeNumbers = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('أحرف (A-Z)'),
              value: _includeLetters,
              onChanged: (value) {
                setState(() {
                  _includeLetters = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('رموز خاصة (!@#)'),
              value: _includeSpecialChars,
              onChanged: (value) {
                setState(() {
                  _includeSpecialChars = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectSelectionSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار المواد',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // خيار جميع المواد
            CheckboxListTile(
              title: const Text('جميع المواد'),
              subtitle: const Text('الكود سيفتح جميع المواد المتاحة'),
              value: _isAllSubjects,
              onChanged: (value) {
                setState(() {
                  _isAllSubjects = value!;
                  if (_isAllSubjects) {
                    _selectedSubjectIds.clear();
                  }
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            if (!_isAllSubjects) ...[
              SizedBox(height: 16.h),
              Text(
                'اختر المواد المحددة:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 8.h),

              if (_isLoadingSubjects)
                const Center(child: CircularProgressIndicator())
              else if (_allSubjects.isEmpty)
                const Text('لا توجد مواد متاحة')
              else
                ..._allSubjects.map(
                  (subject) => CheckboxListTile(
                    title: Text(subject.name),
                    subtitle: Text(subject.description),
                    value: _selectedSubjectIds.contains(subject.id),
                    onChanged: (value) {
                      setState(() {
                        if (value!) {
                          _selectedSubjectIds.add(subject.id);
                        } else {
                          _selectedSubjectIds.remove(subject.id);
                        }
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
            ],

            if (!_isAllSubjects && _selectedSubjectIds.isNotEmpty) ...[
              SizedBox(height: 16.h),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'تم اختيار ${_selectedSubjectIds.length} مادة',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVideoSubjectSelectionSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الفيديوهات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // خيار جميع الفيديوهات
            CheckboxListTile(
              title: const Text('جميع الفيديوهات'),
              subtitle: const Text('الكود سيفتح جميع الفيديوهات المتاحة'),
              value: _isAllVideoSubjects,
              onChanged: (value) {
                setState(() {
                  _isAllVideoSubjects = value!;
                  if (_isAllVideoSubjects) {
                    _selectedVideoSubjectIds.clear();
                  }
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            if (!_isAllVideoSubjects) ...[
              SizedBox(height: 16.h),
              Text(
                'اختر مواد الفيديوهات المحددة:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 8.h),

              if (_isLoadingVideoSubjects)
                const Center(child: CircularProgressIndicator())
              else if (_allVideoSubjects.isEmpty)
                const Text('لا توجد مواد فيديوهات متاحة')
              else
                ..._allVideoSubjects.map(
                  (videoSubject) => CheckboxListTile(
                    title: Text(videoSubject.name),
                    subtitle: Text(videoSubject.description),
                    value: _selectedVideoSubjectIds.contains(videoSubject.id),
                    onChanged: (value) {
                      setState(() {
                        if (value!) {
                          _selectedVideoSubjectIds.add(videoSubject.id);
                        } else {
                          _selectedVideoSubjectIds.remove(videoSubject.id);
                        }
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
            ],

            if (!_isAllVideoSubjects &&
                _selectedVideoSubjectIds.isNotEmpty) ...[
              SizedBox(height: 16.h),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'تم اختيار ${_selectedVideoSubjectIds.length} مادة فيديوهات',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة الكود',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppTheme.textSecondaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _generateSampleCode(),
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                  color: AppTheme.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 8.h),

            Text(
              'مثال على شكل الكود الذي سيتم إنشاؤه',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        SizedBox(width: 16.w),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _generateCodes,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'إنشاء الأكواد',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  String _generateSampleCode() {
    if (!_includeNumbers && !_includeLetters && !_includeSpecialChars) {
      return 'يرجى اختيار نوع واحد على الأقل';
    }

    String chars = '';
    if (_includeNumbers) chars += '0123456789';
    if (_includeLetters) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (_includeSpecialChars) chars += '!@#\$%^&*';

    final random = Random();
    String code = _prefixController.text;

    for (int i = 0; i < _codeLength; i++) {
      code += chars[random.nextInt(chars.length)];
    }

    return code;
  }

  String _getSelectedSubjectNames() {
    return _selectedSubjectIds
        .map((id) => _allSubjects.firstWhere((s) => s.id == id).name)
        .join(', ');
  }

  String _getSelectedVideoSubjectNames() {
    return _selectedVideoSubjectIds
        .map((id) => _allVideoSubjects.firstWhere((s) => s.id == id).name)
        .join(', ');
  }

  String _buildCodeNotes() {
    List<String> notes = [];

    if (_isAllSubjects) {
      notes.add('جميع المواد');
    } else if (_selectedSubjectIds.isNotEmpty) {
      notes.add('المواد: ${_getSelectedSubjectNames()}');
    }

    if (_isAllVideoSubjects) {
      notes.add('جميع الفيديوهات');
    } else if (_selectedVideoSubjectIds.isNotEmpty) {
      notes.add('الفيديوهات: ${_getSelectedVideoSubjectNames()}');
    }

    return notes.isEmpty
        ? 'كود تم إنشاؤه تلقائياً'
        : '${notes.join(' - ')} - تم إنشاؤه تلقائياً';
  }

  Future<void> _generateCodes() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_includeNumbers && !_includeLetters && !_includeSpecialChars) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار نوع واحد على الأقل من الأحرف'),
        ),
      );
      return;
    }

    // التحقق من اختيار المواد أو الفيديوهات
    if (!_isAllSubjects &&
        _selectedSubjectIds.isEmpty &&
        !_isAllVideoSubjects &&
        _selectedVideoSubjectIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'يرجى اختيار مادة أو فيديو واحد على الأقل أو تحديد جميع المواد/الفيديوهات',
          ),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final quantity = int.parse(_quantityController.text);
      final duration = int.parse(_durationController.text);
      final prefix = _prefixController.text;

      String chars = '';
      if (_includeNumbers) chars += '0123456789';
      if (_includeLetters) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      if (_includeSpecialChars) chars += '!@#\$%^&*';

      final random = Random();
      final batch = FirebaseFirestore.instance.batch();
      final now = DateTime.now();
      final expiryDate = now.add(Duration(days: duration));
      final List<SubscriptionCode> generatedCodes = [];

      for (int i = 0; i < quantity; i++) {
        String code = prefix;
        for (int j = 0; j < _codeLength; j++) {
          code += chars[random.nextInt(chars.length)];
        }

        // تحديد المواد المرتبطة بالكود
        List<String> codeSubjectIds;
        if (_isAllSubjects) {
          codeSubjectIds = _allSubjects.map((s) => s.id).toList();
        } else {
          codeSubjectIds = List.from(_selectedSubjectIds);
        }

        // تحديد الفيديوهات المرتبطة بالكود
        List<String> codeVideoSubjectIds;
        if (_isAllVideoSubjects) {
          codeVideoSubjectIds = _allVideoSubjects.map((s) => s.id).toList();
        } else {
          codeVideoSubjectIds = List.from(_selectedVideoSubjectIds);
        }

        final subscriptionCode = SubscriptionCode(
          id: DateTime.now().millisecondsSinceEpoch.toString() + i.toString(),
          code: code,
          subjectIds: codeSubjectIds,
          videoSubjectIds: codeVideoSubjectIds,
          status: CodeStatus.active,
          createdAt: now,
          expiresAt: expiryDate,
          createdByAdminId: 'admin',
          notes: _buildCodeNotes(),
        );

        final docRef = FirebaseFirestore.instance
            .collection('subscription_codes')
            .doc(subscriptionCode.id);

        batch.set(docRef, subscriptionCode.toMap());
        generatedCodes.add(subscriptionCode);
      }

      // إضافة timeout لعملية الحفظ
      await batch.commit().timeout(
        const Duration(seconds: 60),
        onTimeout: () =>
            throw Exception('انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى'),
      );

      if (mounted) {
        List<String> infoList = [];

        if (_isAllSubjects) {
          infoList.add('لجميع المواد');
        } else if (_selectedSubjectIds.isNotEmpty) {
          infoList.add('للمواد: ${_getSelectedSubjectNames()}');
        }

        if (_isAllVideoSubjects) {
          infoList.add('لجميع الفيديوهات');
        } else if (_selectedVideoSubjectIds.isNotEmpty) {
          infoList.add('للفيديوهات: ${_getSelectedVideoSubjectNames()}');
        }

        final info = infoList.isEmpty ? '' : ' ${infoList.join(' و ')}';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء $quantity كود بنجاح$info'),
            backgroundColor: AppTheme.successColor,
          ),
        );

        widget.onCodesGenerated?.call();

        // عرض الأكواد المُنشأة مع الباركود
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => GeneratedCodesDisplayWidget(
              codes: generatedCodes,
              onClose: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إنشاء الأكواد: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
