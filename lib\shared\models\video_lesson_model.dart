import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج دروس الفيديوهات
class VideoLesson {
  final String id;
  final String unitId; // معرف الوحدة
  final String subjectId; // معرف المادة
  final String sectionId; // معرف القسم
  final String name;
  final String description;
  final String iconUrl;
  final String color;
  final int order;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  const VideoLesson({
    required this.id,
    required this.unitId,
    required this.subjectId,
    required this.sectionId,
    required this.name,
    required this.description,
    this.iconUrl = '',
    this.color = '#6C5CE7',
    this.order = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
  });

  factory VideoLesson.fromMap(Map<String, dynamic> map) {
    return VideoLesson(
      id: map['id'] ?? '',
      unitId: map['unitId'] ?? '',
      subjectId: map['subjectId'] ?? '',
      sectionId: map['sectionId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      order: map['order'] ?? 0,
      isActive: map['isActive'] ?? true,
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'order': order,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'createdByAdminId': createdByAdminId,
    };
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  VideoLesson copyWith({
    String? id,
    String? unitId,
    String? subjectId,
    String? sectionId,
    String? name,
    String? description,
    String? iconUrl,
    String? color,
    int? order,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
  }) {
    return VideoLesson(
      id: id ?? this.id,
      unitId: unitId ?? this.unitId,
      subjectId: subjectId ?? this.subjectId,
      sectionId: sectionId ?? this.sectionId,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }

  // دوال Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'order': order,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdByAdminId': createdByAdminId,
    };
  }

  factory VideoLesson.fromFirestore(Map<String, dynamic> data, String documentId) {
    return VideoLesson(
      id: documentId,
      unitId: data['unitId'] ?? '',
      subjectId: data['subjectId'] ?? '',
      sectionId: data['sectionId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      iconUrl: data['iconUrl'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      createdByAdminId: data['createdByAdminId'] ?? '',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoLesson && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoLesson(id: $id, unitId: $unitId, subjectId: $subjectId, sectionId: $sectionId, name: $name, description: $description, isActive: $isActive)';
  }
}
