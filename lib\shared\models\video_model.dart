import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة تحميل الفيديو
enum VideoDownloadStatus {
  notDownloaded, // غير محمل
  downloading, // قيد التحميل
  downloaded, // محمل
  failed, // فشل التحميل
}

/// جودة الفيديو
enum VideoQuality {
  low, // جودة منخفضة
  medium, // جودة متوسطة
  high, // جودة عالية
  auto, // تلقائي حسب الشبكة
}

/// نموذج الفيديو مع التشفير والحماية
class Video {
  final String id;
  final String lessonId; // معرف الدرس
  final String unitId; // معرف الوحدة
  final String subjectId; // معرف المادة
  final String sectionId; // معرف القسم
  final String title; // عنوان الفيديو
  final String description; // وصف الفيديو
  final String encryptedUrl; // الرابط المشفر للفيديو
  final String thumbnailUrl; // صورة مصغرة للفيديو
  final int durationInSeconds; // مدة الفيديو بالثواني
  final VideoQuality quality; // جودة الفيديو
  final int fileSizeInBytes; // حجم الملف بالبايت
  final int order; // ترتيب الفيديو في الدرس
  final bool isActive; // هل الفيديو نشط
  final bool isPreview; // هل هو فيديو معاينة مجاني
  final Map<String, dynamic> metadata; // بيانات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;
  
  // بيانات التحميل المحلي
  final VideoDownloadStatus downloadStatus;
  final String? localEncryptedPath; // مسار الملف المشفر المحلي
  final DateTime? downloadedAt; // تاريخ التحميل
  final String? downloadHash; // hash للتحقق من سلامة الملف

  const Video({
    required this.id,
    required this.lessonId,
    required this.unitId,
    required this.subjectId,
    required this.sectionId,
    required this.title,
    required this.description,
    required this.encryptedUrl,
    this.thumbnailUrl = '',
    this.durationInSeconds = 0,
    this.quality = VideoQuality.auto,
    this.fileSizeInBytes = 0,
    this.order = 0,
    this.isActive = true,
    this.isPreview = false,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
    this.downloadStatus = VideoDownloadStatus.notDownloaded,
    this.localEncryptedPath,
    this.downloadedAt,
    this.downloadHash,
  });

  factory Video.fromMap(Map<String, dynamic> map) {
    return Video(
      id: map['id'] ?? '',
      lessonId: map['lessonId'] ?? '',
      unitId: map['unitId'] ?? '',
      subjectId: map['subjectId'] ?? '',
      sectionId: map['sectionId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      encryptedUrl: map['encryptedUrl'] ?? '',
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      durationInSeconds: map['durationInSeconds'] ?? 0,
      quality: VideoQuality.values.firstWhere(
        (q) => q.name == map['quality'],
        orElse: () => VideoQuality.auto,
      ),
      fileSizeInBytes: map['fileSizeInBytes'] ?? 0,
      order: map['order'] ?? 0,
      isActive: map['isActive'] ?? true,
      isPreview: map['isPreview'] ?? false,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
      downloadStatus: VideoDownloadStatus.values.firstWhere(
        (s) => s.name == map['downloadStatus'],
        orElse: () => VideoDownloadStatus.notDownloaded,
      ),
      localEncryptedPath: map['localEncryptedPath'],
      downloadedAt: map['downloadedAt'] != null ? _parseDateTime(map['downloadedAt']) : null,
      downloadHash: map['downloadHash'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'lessonId': lessonId,
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'title': title,
      'description': description,
      'encryptedUrl': encryptedUrl,
      'thumbnailUrl': thumbnailUrl,
      'durationInSeconds': durationInSeconds,
      'quality': quality.name,
      'fileSizeInBytes': fileSizeInBytes,
      'order': order,
      'isActive': isActive,
      'isPreview': isPreview,
      'metadata': metadata,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'createdByAdminId': createdByAdminId,
      'downloadStatus': downloadStatus.name,
      'localEncryptedPath': localEncryptedPath,
      'downloadedAt': downloadedAt?.millisecondsSinceEpoch,
      'downloadHash': downloadHash,
    };
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  Video copyWith({
    String? id,
    String? lessonId,
    String? unitId,
    String? subjectId,
    String? sectionId,
    String? title,
    String? description,
    String? encryptedUrl,
    String? thumbnailUrl,
    int? durationInSeconds,
    VideoQuality? quality,
    int? fileSizeInBytes,
    int? order,
    bool? isActive,
    bool? isPreview,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
    VideoDownloadStatus? downloadStatus,
    String? localEncryptedPath,
    DateTime? downloadedAt,
    String? downloadHash,
  }) {
    return Video(
      id: id ?? this.id,
      lessonId: lessonId ?? this.lessonId,
      unitId: unitId ?? this.unitId,
      subjectId: subjectId ?? this.subjectId,
      sectionId: sectionId ?? this.sectionId,
      title: title ?? this.title,
      description: description ?? this.description,
      encryptedUrl: encryptedUrl ?? this.encryptedUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      durationInSeconds: durationInSeconds ?? this.durationInSeconds,
      quality: quality ?? this.quality,
      fileSizeInBytes: fileSizeInBytes ?? this.fileSizeInBytes,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      isPreview: isPreview ?? this.isPreview,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
      downloadStatus: downloadStatus ?? this.downloadStatus,
      localEncryptedPath: localEncryptedPath ?? this.localEncryptedPath,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      downloadHash: downloadHash ?? this.downloadHash,
    );
  }

  // دوال Firestore (بدون بيانات التحميل المحلي)
  Map<String, dynamic> toFirestore() {
    return {
      'lessonId': lessonId,
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'title': title,
      'description': description,
      'encryptedUrl': encryptedUrl,
      'thumbnailUrl': thumbnailUrl,
      'durationInSeconds': durationInSeconds,
      'quality': quality.name,
      'fileSizeInBytes': fileSizeInBytes,
      'order': order,
      'isActive': isActive,
      'isPreview': isPreview,
      'metadata': metadata,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdByAdminId': createdByAdminId,
    };
  }

  factory Video.fromFirestore(Map<String, dynamic> data, String documentId) {
    return Video(
      id: documentId,
      lessonId: data['lessonId'] ?? '',
      unitId: data['unitId'] ?? '',
      subjectId: data['subjectId'] ?? '',
      sectionId: data['sectionId'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      encryptedUrl: data['encryptedUrl'] ?? '',
      thumbnailUrl: data['thumbnailUrl'] ?? '',
      durationInSeconds: data['durationInSeconds'] ?? 0,
      quality: VideoQuality.values.firstWhere(
        (q) => q.name == data['quality'],
        orElse: () => VideoQuality.auto,
      ),
      fileSizeInBytes: data['fileSizeInBytes'] ?? 0,
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      isPreview: data['isPreview'] ?? false,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      createdByAdminId: data['createdByAdminId'] ?? '',
    );
  }

  /// تحويل مدة الفيديو إلى نص قابل للقراءة
  String get formattedDuration {
    final hours = durationInSeconds ~/ 3600;
    final minutes = (durationInSeconds % 3600) ~/ 60;
    final seconds = durationInSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// تحويل حجم الملف إلى نص قابل للقراءة
  String get formattedFileSize {
    if (fileSizeInBytes < 1024) {
      return '$fileSizeInBytes B';
    } else if (fileSizeInBytes < 1024 * 1024) {
      return '${(fileSizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (fileSizeInBytes < 1024 * 1024 * 1024) {
      return '${(fileSizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// هل الفيديو محمل محلياً
  bool get isDownloaded => downloadStatus == VideoDownloadStatus.downloaded && localEncryptedPath != null;

  /// هل الفيديو قيد التحميل
  bool get isDownloading => downloadStatus == VideoDownloadStatus.downloading;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Video && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Video(id: $id, title: $title, lessonId: $lessonId, isActive: $isActive, downloadStatus: $downloadStatus)';
  }
}
