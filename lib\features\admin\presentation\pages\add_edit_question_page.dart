import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/models/lesson_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/image_service.dart';

class AddEditQuestionPage extends StatefulWidget {
  final Question? question;
  final List<Subject> subjects;
  final bool isCourse;
  final VoidCallback? onQuestionSaved;

  const AddEditQuestionPage({
    super.key,
    this.question,
    required this.subjects,
    required this.isCourse,
    this.onQuestionSaved,
  });

  @override
  State<AddEditQuestionPage> createState() => _AddEditQuestionPageState();
}

class _AddEditQuestionPageState extends State<AddEditQuestionPage> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _explanationController = TextEditingController();

  // دالة لتحديد اتجاه النص بذكاء
  TextDirection _getTextDirection(String text) {
    if (text.isEmpty) return TextDirection.rtl;

    // البحث عن أول حرف له اتجاه محدد
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final code = char.codeUnitAt(0);

      // الأحرف العربية (0x0600 - 0x06FF)
      if ((code >= 0x0600 && code <= 0x06FF) ||
          // الأحرف العربية الممتدة (0x0750 - 0x077F)
          (code >= 0x0750 && code <= 0x077F) ||
          // الأحرف العربية التكميلية (0x08A0 - 0x08FF)
          (code >= 0x08A0 && code <= 0x08FF) ||
          // الأحرف العربية في Unicode (0xFB50 - 0xFDFF)
          (code >= 0xFB50 && code <= 0xFDFF) ||
          // الأحرف العربية في Unicode (0xFE70 - 0xFEFF)
          (code >= 0xFE70 && code <= 0xFEFF)) {
        return TextDirection.rtl;
      }

      // الأحرف اللاتينية (A-Z, a-z)
      if ((code >= 0x0041 && code <= 0x005A) ||
          (code >= 0x0061 && code <= 0x007A)) {
        return TextDirection.ltr;
      }
    }

    // إذا لم نجد أحرف محددة، نستخدم RTL كافتراضي
    return TextDirection.rtl;
  }

  List<TextEditingController> _optionControllers = [];
  List<Unit> _units = [];
  List<Lesson> _lessons = [];

  String? _selectedSubjectId;
  String? _selectedUnitId;
  String? _selectedLessonId;
  QuestionType _selectedType = QuestionType.multipleChoice;
  QuestionContentType _selectedContentType = QuestionContentType.text;

  int _correctAnswerIndex = 0;
  bool _isLoading = false;

  // متغيرات الصورة
  File? _selectedImageFile;
  String? _currentImageUrl;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.question != null) {
      // تعديل سؤال موجود
      final question = widget.question!;
      _questionController.text = question.questionText;
      _explanationController.text = question.explanation;
      _selectedSubjectId = question.subjectId;
      _selectedUnitId = question.unitId.isEmpty ? null : question.unitId;
      _selectedLessonId = question.lessonId.isEmpty ? null : question.lessonId;
      _selectedType = question.type;
      _selectedContentType = question.contentType;
      _currentImageUrl = question.imageUrl.isEmpty ? null : question.imageUrl;
      _correctAnswerIndex = question.options.indexOf(
        question.correctAnswers.first,
      );

      // إعداد الخيارات
      _optionControllers = question.options
          .map((option) => TextEditingController(text: option))
          .toList();
    } else {
      // إضافة سؤال جديد
      _addOption();
      _addOption();
      _addOption();
      _addOption();
    }

    if (_selectedSubjectId != null) {
      _loadUnits(_selectedSubjectId!);
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _explanationController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadUnits(String subjectId) async {
    try {
      final units = await ContentService.instance.getSubjectUnits(subjectId);
      setState(() {
        _units = units;
        if (_selectedUnitId != null &&
            !units.any((unit) => unit.id == _selectedUnitId)) {
          _selectedUnitId = null;
          _selectedLessonId = null;
          _lessons.clear();
        }
      });

      if (_selectedUnitId != null) {
        _loadLessons(_selectedUnitId!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الوحدات: $e')));
      }
    }
  }

  Future<void> _loadLessons(String unitId) async {
    try {
      final lessons = await ContentService.instance.getUnitLessons(unitId);
      setState(() {
        _lessons = lessons;
        if (_selectedLessonId != null &&
            !lessons.any((lesson) => lesson.id == _selectedLessonId)) {
          _selectedLessonId = null;
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الدروس: $e')));
      }
    }
  }

  void _addOption() {
    setState(() {
      _optionControllers.add(TextEditingController());
    });
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
        if (_correctAnswerIndex >= _optionControllers.length) {
          _correctAnswerIndex = _optionControllers.length - 1;
        }
      });
    }
  }

  // دوال اختيار الصور
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImageFile = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في اختيار الصورة: $e')));
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImageFile = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التقاط الصورة: $e')));
      }
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImageFile = null;
      _currentImageUrl = null;
    });
  }

  void _showImagePickerDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختيار صورة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('من المعرض'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('التقاط صورة'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.question != null ? 'تعديل السؤال' : 'إضافة سؤال جديد',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: widget.isCourse
                ? AppTheme.secondaryGradient
                : AppTheme.primaryGradient,
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveQuestion,
              child: Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildBasicInfoSection(),

              SizedBox(height: 24.h),

              // نص السؤال
              _buildQuestionTextSection(),

              SizedBox(height: 24.h),

              // الخيارات
              _buildOptionsSection(),

              SizedBox(height: 24.h),

              // الشرح
              _buildExplanationSection(),

              SizedBox(height: 32.h),

              // أزرار الحفظ والإلغاء
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // المادة
            DropdownButtonFormField<String>(
              value: _selectedSubjectId,
              decoration: const InputDecoration(
                labelText: 'المادة *',
                border: OutlineInputBorder(),
              ),
              items: widget.subjects
                  .map(
                    (subject) => DropdownMenuItem(
                      value: subject.id,
                      child: Text(subject.name),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSubjectId = value;
                  _selectedUnitId = null;
                  _selectedLessonId = null;
                  _units.clear();
                  _lessons.clear();
                });
                if (value != null) {
                  _loadUnits(value);
                }
              },
              validator: (value) => value == null ? 'يرجى اختيار المادة' : null,
            ),

            SizedBox(height: 16.h),

            // الوحدة
            DropdownButtonFormField<String>(
              value: _selectedUnitId,
              decoration: const InputDecoration(
                labelText: 'الوحدة',
                border: OutlineInputBorder(),
              ),
              items: _units
                  .map(
                    (unit) => DropdownMenuItem(
                      value: unit.id,
                      child: Text(unit.name),
                    ),
                  )
                  .toList(),
              onChanged: _selectedSubjectId == null
                  ? null
                  : (value) {
                      setState(() {
                        _selectedUnitId = value;
                        _selectedLessonId = null;
                        _lessons.clear();
                      });
                      if (value != null) {
                        _loadLessons(value);
                      }
                    },
            ),

            SizedBox(height: 16.h),

            // الدرس
            DropdownButtonFormField<String>(
              value: _selectedLessonId,
              decoration: const InputDecoration(
                labelText: 'الدرس',
                border: OutlineInputBorder(),
              ),
              items: _lessons
                  .map(
                    (lesson) => DropdownMenuItem(
                      value: lesson.id,
                      child: Text(lesson.name),
                    ),
                  )
                  .toList(),
              onChanged: _selectedUnitId == null
                  ? null
                  : (value) {
                      setState(() {
                        _selectedLessonId = value;
                      });
                    },
            ),

            SizedBox(height: 16.h),

            // نوع السؤال ومستوى الصعوبة
            Column(
              children: [
                // الصف الأول: نوع السؤال ومستوى الصعوبة
                Row(
                  children: [
                    // نوع السؤال
                    Expanded(
                      child: DropdownButtonFormField<QuestionType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع السؤال',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        style: TextStyle(fontSize: 11.sp, color: Colors.black),
                        items: QuestionType.values
                            .map(
                              (type) => DropdownMenuItem(
                                value: type,
                                child: Text(
                                  _getQuestionTypeDisplayName(type),
                                  style: TextStyle(fontSize: 11.sp),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 16.h),

                // نوع المحتوى
                DropdownButtonFormField<QuestionContentType>(
                  value: _selectedContentType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المحتوى',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  style: TextStyle(fontSize: 11.sp, color: Colors.black),
                  items: QuestionContentType.values
                      .map(
                        (type) => DropdownMenuItem(
                          value: type,
                          child: Text(
                            type == QuestionContentType.text ? 'نص' : 'صورة',
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        ),
                      )
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedContentType = value!;
                    });
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionTextSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _selectedContentType == QuestionContentType.text
                  ? 'نص السؤال'
                  : 'صورة السؤال',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            if (_selectedContentType == QuestionContentType.text) ...[
              StatefulBuilder(
                builder: (context, setTextState) {
                  return TextFormField(
                    controller: _questionController,
                    textDirection: _getTextDirection(_questionController.text),
                    decoration: const InputDecoration(
                      labelText: 'اكتب السؤال هنا *',
                      border: OutlineInputBorder(),
                      alignLabelWithHint: true,
                    ),
                    maxLines: 4,
                    onChanged: (value) {
                      setTextState(() {
                        // تحديث اتجاه النص عند تغيير المحتوى
                      });
                    },
                    validator: (value) {
                      if (_selectedContentType == QuestionContentType.text &&
                          (value == null || value.trim().isEmpty)) {
                        return 'يرجى كتابة نص السؤال';
                      }
                      return null;
                    },
                  );
                },
              ),
            ] else ...[
              // قسم اختيار الصورة
              Container(
                width: double.infinity,
                height: 200.h,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: _selectedImageFile != null
                    ? Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.file(
                              _selectedImageFile!,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: IconButton(
                              onPressed: _removeImage,
                              icon: const Icon(Icons.close, color: Colors.red),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.white,
                                shape: const CircleBorder(),
                              ),
                            ),
                          ),
                        ],
                      )
                    : _currentImageUrl != null
                    ? Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              _currentImageUrl!,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(Icons.error, color: Colors.red),
                                );
                              },
                            ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: IconButton(
                              onPressed: _removeImage,
                              icon: const Icon(Icons.close, color: Colors.red),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.white,
                                shape: const CircleBorder(),
                              ),
                            ),
                          ),
                        ],
                      )
                    : InkWell(
                        onTap: _showImagePickerDialog,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_photo_alternate,
                              size: 48.sp,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'اضغط لاختيار صورة',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),

              SizedBox(height: 8.h),

              // أزرار اختيار الصورة
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromGallery,
                      icon: const Icon(Icons.photo_library),
                      label: const Text('من المعرض'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromCamera,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('التقاط صورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.secondaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'الخيارات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _addOption,
                  icon: const Icon(Icons.add),
                  tooltip: 'إضافة خيار',
                ),
              ],
            ),

            SizedBox(height: 16.h),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _optionControllers.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: Row(
                    textDirection: TextDirection.rtl, // من اليمين لليسار
                    children: [
                      // راديو للإجابة الصحيحة
                      Radio<int>(
                        value: index,
                        groupValue: _correctAnswerIndex,
                        onChanged: (value) {
                          setState(() {
                            _correctAnswerIndex = value!;
                          });
                        },
                      ),

                      // نص الخيار
                      Expanded(
                        child: StatefulBuilder(
                          builder: (context, setOptionState) {
                            return TextFormField(
                              controller: _optionControllers[index],
                              textDirection: _getTextDirection(
                                _optionControllers[index].text,
                              ),
                              decoration: InputDecoration(
                                labelText: 'الخيار ${index + 1}',
                                border: const OutlineInputBorder(),
                                suffixIcon: _optionControllers.length > 2
                                    ? IconButton(
                                        onPressed: () => _removeOption(index),
                                        icon: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                        ),
                                      )
                                    : null,
                              ),
                              onChanged: (value) {
                                setOptionState(() {
                                  // تحديث اتجاه النص عند تغيير المحتوى
                                });
                              },
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'يرجى كتابة نص الخيار';
                                }
                                return null;
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            if (_optionControllers.length < 6)
              Center(
                child: TextButton.icon(
                  onPressed: _addOption,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة خيار آخر'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الشرح (اختياري)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            StatefulBuilder(
              builder: (context, setExplanationState) {
                return TextFormField(
                  controller: _explanationController,
                  textDirection: _getTextDirection(_explanationController.text),
                  decoration: const InputDecoration(
                    labelText: 'شرح الإجابة الصحيحة',
                    border: OutlineInputBorder(),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    setExplanationState(() {
                      // تحديث اتجاه النص عند تغيير المحتوى
                    });
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        SizedBox(width: 16.w),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveQuestion,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.isCourse
                  ? AppTheme.accentColor
                  : AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.question != null ? 'تحديث السؤال' : 'إضافة السؤال',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveQuestion() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من نوع المحتوى والمتطلبات
    if (_selectedContentType == QuestionContentType.text) {
      if (_questionController.text.trim().isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('يرجى إدخال نص السؤال')));
        return;
      }
    } else if (_selectedContentType == QuestionContentType.image) {
      if (_selectedImageFile == null && _currentImageUrl == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى اختيار صورة للسؤال')),
        );
        return;
      }
    }

    // التحقق من وجود خيارات صحيحة
    final options = _optionControllers.map((c) => c.text.trim()).toList();
    if (options.any((option) => option.isEmpty)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى ملء جميع الخيارات')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      String imageUrl = _currentImageUrl ?? '';

      // رفع الصورة إذا تم اختيار صورة جديدة
      if (_selectedImageFile != null) {
        final imageBytes = await _selectedImageFile!.readAsBytes();
        final questionId =
            widget.question?.id ??
            DateTime.now().millisecondsSinceEpoch.toString();
        imageUrl = await ImageService().uploadQuestionImage(
          imageBytes,
          questionId,
        );
      }

      final question = Question(
        id:
            widget.question?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        questionText: _selectedContentType == QuestionContentType.text
            ? _questionController.text.trim()
            : '', // نص فارغ للصور
        options: options,
        correctAnswers: [options[_correctAnswerIndex]],
        explanation: _explanationController.text.trim(),
        type: _selectedType,
        difficulty: DifficultyLevel.medium, // قيمة ثابتة للصعوبة
        contentType: _selectedContentType,
        points: 1, // قيمة ثابتة للنقاط
        subjectId: _selectedSubjectId!,
        unitId: _selectedUnitId ?? '',
        lessonId: _selectedLessonId ?? '',
        isCourseQuestion: widget.isCourse,
        isActive: true,
        imageUrl: imageUrl,
        metadata: {},
        createdByAdminId: 'admin',
        createdAt: widget.question?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.question != null) {
        await ExamService.instance.updateQuestion(question);
      } else {
        await ExamService.instance.createQuestion(question);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.question != null
                  ? 'تم تحديث السؤال بنجاح'
                  : 'تم إضافة السؤال بنجاح',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );

        widget.onQuestionSaved?.call();
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ السؤال: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getQuestionTypeDisplayName(QuestionType type) {
    switch (type) {
      case QuestionType.multipleChoice:
        return 'اختيار من متعدد';
      case QuestionType.trueFalse:
        return 'صح أم خطأ';
      case QuestionType.shortAnswer:
        return 'إجابة قصيرة';
      case QuestionType.essay:
        return 'مقال';
      case QuestionType.matching:
        return 'مطابقة';
      case QuestionType.fillInTheBlank:
        return 'ملء الفراغات';
    }
  }
}
