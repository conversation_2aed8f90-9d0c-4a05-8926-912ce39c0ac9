import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';

import 'flavors.dart';
import 'app.dart';
import 'firebase_options_student.dart';
import 'firebase_options_admin.dart' as admin_options;
import 'shared/services/encryption_service.dart';
import 'shared/services/video_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تحديد النكهة بناءً على متغير البيئة
  const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'student');

  if (flavor == 'admin') {
    F.appFlavor = Flavor.admin;
    await Firebase.initializeApp(
      options: admin_options.DefaultFirebaseOptions.currentPlatform,
    );
  } else {
    F.appFlavor = Flavor.student;
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  // تهيئة خدمة التشفير
  EncryptionService.instance.initialize();

  // تهيئة خدمة الفيديو
  await VideoService.instance.initialize();

  runApp(const MyApp());
}
