import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import 'flavors.dart';
import 'core/theme/app_theme.dart';
import 'features/student/presentation/pages/student_home_page.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';
import 'shared/services/subscription_service.dart';
import 'shared/services/exam_service.dart';
import 'shared/services/content_service.dart';
import 'shared/services/pricing_service.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الاشتراكات عند بدء التطبيق
    if (F.isStudent) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        SubscriptionService.instance.initialize();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SubscriptionService.instance),
            ChangeNotifierProvider(create: (_) => ExamService.instance),
            ChangeNotifierProvider(create: (_) => ContentService.instance),
            ChangeNotifierProvider(create: (_) => PricingService.instance),
          ],
          child: MaterialApp(
            title: F.title,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            home: F.isStudent ? const StudentHomePage() : const AdminHomePage(),
          ),
        );
      },
    );
  }
}
