import 'package:flutter/material.dart';
import '../../../../shared/models/video_lesson_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/theme/app_theme.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/color_picker_widget.dart';

/// صفحة إضافة/تعديل درس الفيديوهات
class AddVideoLessonPage extends StatefulWidget {
  final String unitId;
  final VideoLesson? lesson;

  const AddVideoLessonPage({super.key, required this.unitId, this.lesson});

  @override
  State<AddVideoLessonPage> createState() => _AddVideoLessonPageState();
}

class _AddVideoLessonPageState extends State<AddVideoLessonPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();

  final VideoService _videoService = VideoService.instance;
  bool _isActive = true;
  bool _isLoading = false;
  String _selectedColor = '#6C5CE7'; // اللون الافتراضي

  bool get _isEditing => widget.lesson != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadLessonData();
    }
  }

  void _loadLessonData() {
    final lesson = widget.lesson!;
    _nameController.text = lesson.name;
    _descriptionController.text = lesson.description;
    _orderController.text = lesson.order.toString();
    _isActive = lesson.isActive;
    _selectedColor = lesson.color;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  Future<void> _saveLesson() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final lesson = VideoLesson(
        id: _isEditing ? widget.lesson!.id : '',
        sectionId: _isEditing ? widget.lesson!.sectionId : '',
        subjectId: _isEditing ? widget.lesson!.subjectId : '',
        unitId: widget.unitId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        color: _selectedColor,
        order: int.tryParse(_orderController.text) ?? 0,
        isActive: _isActive,
        createdAt: _isEditing ? widget.lesson!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
        createdByAdminId: 'admin_id', // TODO: استخدام معرف الأدمن الحقيقي
      );

      if (_isEditing) {
        await _videoService.updateVideoLesson(lesson);
      } else {
        await _videoService.addVideoLesson(lesson);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث الدرس بنجاح' : 'تم إضافة الدرس بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الدرس: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing ? 'تعديل درس الفيديوهات' : 'إضافة درس فيديوهات جديد',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildLessonInfo(),
                    const SizedBox(height: 24),
                    _buildFormFields(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildLessonInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  _isEditing ? 'تعديل درس الفيديوهات' : 'درس فيديوهات جديد',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isEditing
                  ? 'قم بتعديل بيانات درس الفيديوهات'
                  : 'أدخل بيانات درس الفيديوهات الجديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _nameController,
          label: 'اسم الدرس',
          hint: 'مثال: الدرس الأول، المعادلات، النظريات',
          prefixIcon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم الدرس';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          label: 'وصف الدرس (اختياري)',
          hint: 'وصف مختصر عن محتوى الدرس',
          prefixIcon: Icons.description,
          maxLines: 3,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _orderController,
          label: 'ترتيب الدرس',
          hint: 'رقم ترتيب الدرس (1، 2، 3...)',
          prefixIcon: Icons.sort,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال ترتيب الدرس';
            }
            final order = int.tryParse(value);
            if (order == null || order < 0) {
              return 'يرجى إدخال رقم صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        _buildColorPicker(),
        const SizedBox(height: 20),
        _buildActiveSwitch(),
      ],
    );
  }

  Widget _buildColorPicker() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ColorPickerWidget(
          selectedColor: _selectedColor,
          onColorSelected: (color) {
            setState(() {
              _selectedColor = color;
            });
          },
          title: 'اختر لون الدرس',
        ),
      ),
    );
  }

  Widget _buildActiveSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة الدرس',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    _isActive
                        ? 'الدرس نشط ومرئي للطلاب'
                        : 'الدرس غير نشط ومخفي',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() => _isActive = value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            backgroundColor: Colors.grey[300]!,
            textColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: _isEditing ? 'تحديث الدرس' : 'إضافة الدرس',
            onPressed: _saveLesson,
            backgroundColor: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }
}
