import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

/// خدمة التشفير والحماية للفيديوهات والروابط
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  static EncryptionService get instance => _instance;
  EncryptionService._internal();

  // مفتاح التشفير الثابت (يجب أن يكون آمناً في الإنتاج)
  static const String _encryptionKey = 'SmartTestVideoSecureKey2024!@#';
  
  late final Encrypter _encrypter;
  late final IV _iv;

  /// تهيئة خدمة التشفير
  void initialize() {
    final key = Key.fromBase64(base64.encode(_encryptionKey.codeUnits));
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }

  /// تشفير نص (للروابط)
  String encryptText(String plainText) {
    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      throw Exception('فشل في تشفير النص: $e');
    }
  }

  /// فك تشفير نص (للروابط)
  String decryptText(String encryptedText) {
    try {
      final encrypted = Encrypted.fromBase64(encryptedText);
      return _encrypter.decrypt(encrypted, iv: _iv);
    } catch (e) {
      throw Exception('فشل في فك تشفير النص: $e');
    }
  }

  /// تشفير بيانات الفيديو (للملفات المحملة)
  Uint8List encryptVideoData(Uint8List videoData) {
    try {
      // تقسيم البيانات إلى أجزاء صغيرة للتشفير
      const chunkSize = 1024 * 1024; // 1MB chunks
      final encryptedChunks = <Uint8List>[];
      
      for (int i = 0; i < videoData.length; i += chunkSize) {
        final end = (i + chunkSize < videoData.length) ? i + chunkSize : videoData.length;
        final chunk = videoData.sublist(i, end);
        
        final encrypted = _encrypter.encryptBytes(chunk, iv: _iv);
        encryptedChunks.add(encrypted.bytes);
      }
      
      // دمج الأجزاء المشفرة
      final totalLength = encryptedChunks.fold<int>(0, (sum, chunk) => sum + chunk.length);
      final result = Uint8List(totalLength);
      int offset = 0;
      
      for (final chunk in encryptedChunks) {
        result.setRange(offset, offset + chunk.length, chunk);
        offset += chunk.length;
      }
      
      return result;
    } catch (e) {
      throw Exception('فشل في تشفير بيانات الفيديو: $e');
    }
  }

  /// فك تشفير بيانات الفيديو (للملفات المحملة)
  Uint8List decryptVideoData(Uint8List encryptedData) {
    try {
      // هذا تبسيط - في الواقع نحتاج لحفظ معلومات الأجزاء
      final encrypted = Encrypted(encryptedData);
      final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
      return Uint8List.fromList(decryptedBytes);
    } catch (e) {
      throw Exception('فشل في فك تشفير بيانات الفيديو: $e');
    }
  }

  /// إنشاء hash للتحقق من سلامة البيانات
  String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من hash البيانات
  bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }

  /// إنشاء مفتاح فريد للجهاز
  String generateDeviceKey(String deviceId) {
    final combined = '$deviceId$_encryptionKey';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32);
  }

  /// تشفير رابط الفيديو مع معلومات إضافية
  Map<String, String> encryptVideoUrl(String url, String videoId, String deviceId) {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final deviceKey = generateDeviceKey(deviceId);
      
      // إنشاء payload مع معلومات الحماية
      final payload = {
        'url': url,
        'videoId': videoId,
        'deviceId': deviceId,
        'timestamp': timestamp,
        'deviceKey': deviceKey,
      };
      
      final jsonPayload = json.encode(payload);
      final encryptedPayload = encryptText(jsonPayload);
      final hash = generateHash(jsonPayload);
      
      return {
        'encryptedData': encryptedPayload,
        'hash': hash,
        'timestamp': timestamp,
      };
    } catch (e) {
      throw Exception('فشل في تشفير رابط الفيديو: $e');
    }
  }

  /// فك تشفير رابط الفيديو مع التحقق من الصحة
  Map<String, dynamic>? decryptVideoUrl(Map<String, String> encryptedData, String deviceId) {
    try {
      final encryptedPayload = encryptedData['encryptedData'];
      final expectedHash = encryptedData['hash'];
      final timestamp = encryptedData['timestamp'];
      
      if (encryptedPayload == null || expectedHash == null || timestamp == null) {
        return null;
      }
      
      // فك التشفير
      final jsonPayload = decryptText(encryptedPayload);
      
      // التحقق من hash
      if (!verifyHash(jsonPayload, expectedHash)) {
        return null;
      }
      
      final payload = json.decode(jsonPayload) as Map<String, dynamic>;
      
      // التحقق من معرف الجهاز
      if (payload['deviceId'] != deviceId) {
        return null;
      }
      
      // التحقق من مفتاح الجهاز
      final expectedDeviceKey = generateDeviceKey(deviceId);
      if (payload['deviceKey'] != expectedDeviceKey) {
        return null;
      }
      
      // التحقق من انتهاء الصلاحية (24 ساعة)
      final payloadTimestamp = int.parse(payload['timestamp']);
      final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
      const validityPeriod = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية
      
      if (currentTimestamp - payloadTimestamp > validityPeriod) {
        return null; // انتهت الصلاحية
      }
      
      return payload;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء اسم ملف مشفر للفيديو المحمل
  String generateEncryptedFileName(String videoId, String deviceId) {
    final combined = '$videoId$deviceId${DateTime.now().millisecondsSinceEpoch}';
    final hash = generateHash(combined);
    return '${hash.substring(0, 16)}.enc'; // ملف مشفر بامتداد .enc
  }
}
