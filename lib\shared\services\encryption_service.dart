import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart' hide Key;

/// خدمة التشفير والحماية للفيديوهات والروابط
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  static EncryptionService get instance => _instance;
  EncryptionService._internal();

  // مفتاح التشفير الثابت (يجب أن يكون آمناً في الإنتاج)
  static const String _encryptionKey = 'SmartTestVideoSecureKey2024!@#';

  late final Encrypter _encrypter;
  late final IV _iv;
  bool _isInitialized = false;

  /// تهيئة خدمة التشفير
  void initialize() {
    if (_isInitialized) {
      return; // تم التهيئة مسبقاً
    }
    // إنشاء مفتاح 256 بت (32 بايت) باستخدام SHA-256
    final keyBytes = sha256.convert(utf8.encode(_encryptionKey)).bytes;
    final key = Key(Uint8List.fromList(keyBytes));
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
    _isInitialized = true;
  }

  /// تشفير نص (للروابط)
  String encryptText(String plainText) {
    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      throw Exception('فشل في تشفير النص: $e');
    }
  }

  /// التحقق من ما إذا كان النص مشفراً
  bool isEncrypted(String text) {
    try {
      // محاولة فك التشفير للتحقق
      Encrypted.fromBase64(text);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تحويل رابط Google Drive إلى رابط تحميل مباشر
  String convertGoogleDriveUrl(String url) {
    try {
      // التحقق من أن الرابط من Google Drive
      if (url.contains('drive.google.com/file/d/')) {
        // استخراج معرف الملف من الرابط
        final regex = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(url);

        if (match != null) {
          final fileId = match.group(1);
          // تحويل إلى رابط تحميل مباشر
          return 'https://drive.google.com/uc?export=download&id=$fileId';
        }
      }

      // إذا لم يكن رابط Google Drive أو لم نتمكن من تحويله، إرجاع الرابط الأصلي
      return url;
    } catch (e) {
      debugPrint('خطأ في تحويل رابط Google Drive: $e');
      return url;
    }
  }

  /// فك تشفير نص (للروابط) مع التحقق من التشفير أولاً
  String decryptText(String text) {
    try {
      // إذا لم يكن النص مشفراً، إرجاعه كما هو
      if (!isEncrypted(text)) {
        // تحويل رابط Google Drive إذا لزم الأمر
        return convertGoogleDriveUrl(text);
      }

      final encrypted = Encrypted.fromBase64(text);
      final decryptedText = _encrypter.decrypt(encrypted, iv: _iv);

      // تحويل رابط Google Drive إذا لزم الأمر
      return convertGoogleDriveUrl(decryptedText);
    } catch (e) {
      // تسجيل الخطأ للمطورين فقط
      debugPrint('خطأ في فك التشفير: $e');
      throw Exception('فشل في فك التشفير');
    }
  }

  /// تشفير بيانات الفيديو (للملفات المحملة)
  Uint8List encryptVideoData(Uint8List videoData) {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      // تقسيم البيانات إلى أجزاء صغيرة للتشفير
      const chunkSize = 1024 * 1024; // 1MB chunks
      final encryptedChunks = <Uint8List>[];
      final chunkSizes = <int>[];

      for (int i = 0; i < videoData.length; i += chunkSize) {
        final end = (i + chunkSize < videoData.length)
            ? i + chunkSize
            : videoData.length;
        final chunk = videoData.sublist(i, end);

        final encrypted = _encrypter.encryptBytes(chunk, iv: _iv);
        encryptedChunks.add(encrypted.bytes);
        chunkSizes.add(encrypted.bytes.length);
      }

      // إنشاء header يحتوي على معلومات الأجزاء
      final headerData = <int>[];
      headerData.add(chunkSizes.length); // عدد الأجزاء
      headerData.addAll(chunkSizes); // أحجام الأجزاء

      // تحويل header إلى bytes
      final headerBytes = Uint8List.fromList(
        headerData
            .map(
              (e) => [
                (e >> 24) & 0xFF,
                (e >> 16) & 0xFF,
                (e >> 8) & 0xFF,
                e & 0xFF,
              ],
            )
            .expand((e) => e)
            .toList(),
      );

      // حساب الحجم الإجمالي
      final headerSize = headerBytes.length;
      final totalDataLength = encryptedChunks.fold<int>(
        0,
        (sum, chunk) => sum + chunk.length,
      );
      final totalLength =
          4 + headerSize + totalDataLength; // 4 bytes لحجم header

      // دمج كل شيء
      final result = Uint8List(totalLength);
      int offset = 0;

      // كتابة حجم header (4 bytes)
      result[offset++] = (headerSize >> 24) & 0xFF;
      result[offset++] = (headerSize >> 16) & 0xFF;
      result[offset++] = (headerSize >> 8) & 0xFF;
      result[offset++] = headerSize & 0xFF;

      // كتابة header
      result.setRange(offset, offset + headerSize, headerBytes);
      offset += headerSize;

      // كتابة البيانات المشفرة
      for (final chunk in encryptedChunks) {
        result.setRange(offset, offset + chunk.length, chunk);
        offset += chunk.length;
      }

      return result;
    } catch (e) {
      throw Exception('فشل في تشفير بيانات الفيديو: $e');
    }
  }

  /// فك تشفير بيانات الفيديو (للملفات المحملة)
  Uint8List decryptVideoData(Uint8List encryptedData) {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      // محاولة فك التشفير بالطريقة الجديدة (مع header)
      try {
        return _decryptWithHeader(encryptedData);
      } catch (e) {
        debugPrint(
          'فشل فك التشفير بالطريقة الجديدة، محاولة الطريقة القديمة: $e',
        );
        // إذا فشلت، جرب الطريقة القديمة (بدون header)
        return _decryptLegacy(encryptedData);
      }
    } catch (e) {
      throw Exception('فشل في فك تشفير بيانات الفيديو: $e');
    }
  }

  /// فك التشفير بالطريقة الجديدة (مع header)
  Uint8List _decryptWithHeader(Uint8List encryptedData) {
    if (encryptedData.length < 4) {
      throw Exception('بيانات مشفرة غير صالحة - حجم صغير جداً');
    }

    // قراءة حجم header (4 bytes)
    int offset = 0;
    final headerSize =
        (encryptedData[offset] << 24) |
        (encryptedData[offset + 1] << 16) |
        (encryptedData[offset + 2] << 8) |
        encryptedData[offset + 3];
    offset += 4;

    if (encryptedData.length < 4 + headerSize) {
      throw Exception('بيانات مشفرة غير صالحة - header مفقود');
    }

    // قراءة header
    final headerBytes = encryptedData.sublist(offset, offset + headerSize);
    offset += headerSize;

    // تحليل header لاستخراج أحجام الأجزاء
    final chunkSizes = <int>[];
    int headerOffset = 0;

    // قراءة عدد الأجزاء
    final numChunks =
        (headerBytes[headerOffset] << 24) |
        (headerBytes[headerOffset + 1] << 16) |
        (headerBytes[headerOffset + 2] << 8) |
        headerBytes[headerOffset + 3];
    headerOffset += 4;

    // قراءة أحجام الأجزاء
    for (int i = 0; i < numChunks; i++) {
      final chunkSize =
          (headerBytes[headerOffset] << 24) |
          (headerBytes[headerOffset + 1] << 16) |
          (headerBytes[headerOffset + 2] << 8) |
          headerBytes[headerOffset + 3];
      chunkSizes.add(chunkSize);
      headerOffset += 4;
    }

    // فك تشفير كل جزء باستخدام الأحجام المحفوظة
    final decryptedChunks = <Uint8List>[];

    for (final chunkSize in chunkSizes) {
      if (offset + chunkSize > encryptedData.length) {
        throw Exception('بيانات مشفرة غير صالحة - جزء مفقود');
      }

      final encryptedChunk = encryptedData.sublist(offset, offset + chunkSize);
      offset += chunkSize;

      final encrypted = Encrypted(encryptedChunk);
      final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
      decryptedChunks.add(Uint8List.fromList(decryptedBytes));
    }

    // دمج الأجزاء المفكوكة التشفير
    final totalLength = decryptedChunks.fold<int>(
      0,
      (sum, chunk) => sum + chunk.length,
    );
    final result = Uint8List(totalLength);
    int resultOffset = 0;

    for (final chunk in decryptedChunks) {
      result.setRange(resultOffset, resultOffset + chunk.length, chunk);
      resultOffset += chunk.length;
    }

    return result;
  }

  /// فك التشفير بالطريقة القديمة (بدون header) - للملفات المحملة سابقاً
  Uint8List _decryptLegacy(Uint8List encryptedData) {
    // محاولة فك التشفير كقطعة واحدة أولاً
    try {
      final encrypted = Encrypted(encryptedData);
      final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
      return Uint8List.fromList(decryptedBytes);
    } catch (e) {
      debugPrint('فشل فك التشفير كقطعة واحدة، محاولة التقسيم: $e');

      // إذا فشل، جرب تقسيم البيانات بناءً على حجم ثابت
      const estimatedChunkSize = 1048592; // 1MB + 16 bytes padding تقريباً
      final decryptedChunks = <Uint8List>[];

      for (int i = 0; i < encryptedData.length; i += estimatedChunkSize) {
        final end = (i + estimatedChunkSize < encryptedData.length)
            ? i + estimatedChunkSize
            : encryptedData.length;
        final encryptedChunk = encryptedData.sublist(i, end);

        try {
          final encrypted = Encrypted(encryptedChunk);
          final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
          decryptedChunks.add(Uint8List.fromList(decryptedBytes));
        } catch (chunkError) {
          debugPrint('فشل في فك تشفير الجزء $i: $chunkError');
          // تجاهل الأجزاء التالفة وأكمل
          continue;
        }
      }

      if (decryptedChunks.isEmpty) {
        throw Exception('فشل في فك تشفير أي جزء من البيانات');
      }

      // دمج الأجزاء المفكوكة التشفير
      final totalLength = decryptedChunks.fold<int>(
        0,
        (sum, chunk) => sum + chunk.length,
      );
      final result = Uint8List(totalLength);
      int resultOffset = 0;

      for (final chunk in decryptedChunks) {
        result.setRange(resultOffset, resultOffset + chunk.length, chunk);
        resultOffset += chunk.length;
      }

      return result;
    }
  }

  /// إنشاء hash للتحقق من سلامة البيانات
  String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من hash البيانات
  bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }

  /// إنشاء مفتاح فريد للجهاز
  String generateDeviceKey(String deviceId) {
    final combined = '$deviceId$_encryptionKey';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32);
  }

  /// تشفير رابط الفيديو مع معلومات إضافية
  Map<String, String> encryptVideoUrl(
    String url,
    String videoId,
    String deviceId,
  ) {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final deviceKey = generateDeviceKey(deviceId);

      // إنشاء payload مع معلومات الحماية
      final payload = {
        'url': url,
        'videoId': videoId,
        'deviceId': deviceId,
        'timestamp': timestamp,
        'deviceKey': deviceKey,
      };

      final jsonPayload = json.encode(payload);
      final encryptedPayload = encryptText(jsonPayload);
      final hash = generateHash(jsonPayload);

      return {
        'encryptedData': encryptedPayload,
        'hash': hash,
        'timestamp': timestamp,
      };
    } catch (e) {
      throw Exception('فشل في تشفير رابط الفيديو: $e');
    }
  }

  /// فك تشفير رابط الفيديو مع التحقق من الصحة
  Map<String, dynamic>? decryptVideoUrl(
    Map<String, String> encryptedData,
    String deviceId,
  ) {
    try {
      final encryptedPayload = encryptedData['encryptedData'];
      final expectedHash = encryptedData['hash'];
      final timestamp = encryptedData['timestamp'];

      if (encryptedPayload == null ||
          expectedHash == null ||
          timestamp == null) {
        return null;
      }

      // فك التشفير
      final jsonPayload = decryptText(encryptedPayload);

      // التحقق من hash
      if (!verifyHash(jsonPayload, expectedHash)) {
        return null;
      }

      final payload = json.decode(jsonPayload) as Map<String, dynamic>;

      // التحقق من معرف الجهاز
      if (payload['deviceId'] != deviceId) {
        return null;
      }

      // التحقق من مفتاح الجهاز
      final expectedDeviceKey = generateDeviceKey(deviceId);
      if (payload['deviceKey'] != expectedDeviceKey) {
        return null;
      }

      // التحقق من انتهاء الصلاحية (24 ساعة)
      final payloadTimestamp = int.parse(payload['timestamp']);
      final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
      const validityPeriod = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية

      if (currentTimestamp - payloadTimestamp > validityPeriod) {
        return null; // انتهت الصلاحية
      }

      return payload;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء اسم ملف مشفر للفيديو المحمل
  String generateEncryptedFileName(String videoId, String deviceId) {
    final combined =
        '$videoId$deviceId${DateTime.now().millisecondsSinceEpoch}';
    final hash = generateHash(combined);
    return '${hash.substring(0, 16)}.enc'; // ملف مشفر بامتداد .enc
  }
}
